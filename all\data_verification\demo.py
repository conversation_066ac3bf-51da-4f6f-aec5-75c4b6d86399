"""
Demonstration script for the Data Verification System.

This script shows how to use the core components of the data verification system
including creating configurations, registering components, and running validations.
"""

import json
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from all.data_verification.core.models import (
    ValidationConfig, ValidationRule, ErrorSeverity, ErrorCategory
)
from all.data_verification.core.engine import ValidationEngine
from all.data_verification.tests.test_core_interfaces import (
    <PERSON>ckFileReader, MockValidator, MockErrorReporter
)


def create_sample_config() -> ValidationConfig:
    """Create a sample validation configuration."""
    config = ValidationConfig()
    
    # Configure file patterns
    config.file_patterns = ["*.csv", "*.json"]
    
    # Add global validation rules
    not_empty_rule = ValidationRule(
        validator_type="not_empty",
        column_pattern=".*",
        priority=100,
        error_message_template="Column {column_name} cannot be empty"
    )
    config.global_rules.append(not_empty_rule)
    
    # Add column-specific rules
    age_rule = ValidationRule(
        validator_type="numeric_range",
        column_pattern="age",
        parameters={"min_value": 0, "max_value": 120},
        priority=10,
        error_message_template="Age in column {column_name} must be between 0 and 120"
    )
    
    email_rule = ValidationRule(
        validator_type="email_format",
        column_pattern="email",
        priority=20,
        error_message_template="Email in column {column_name} has invalid format"
    )
    
    config.column_rules["age"] = [age_rule]
    config.column_rules["email"] = [email_rule]
    
    # Configure processing settings
    config.file_processing.batch_size = 1000
    config.file_processing.parallel_processing = True
    config.file_processing.memory_limit_mb = 512
    
    # Configure error reporting
    config.error_reporting.console_output = True
    config.error_reporting.file_output = True
    config.error_reporting.max_errors_per_file = 100
    
    # Configure system settings
    config.system.max_parallel_files = 4
    config.system.memory_limit_gb = 8
    config.system.qr3d_integration = True
    
    return config


def demonstrate_basic_usage():
    """Demonstrate basic usage of the data verification system."""
    print("=== Data Verification System Demo ===\n")
    
    # 1. Create configuration
    print("1. Creating validation configuration...")
    config = create_sample_config()
    print(f"   - File patterns: {config.file_patterns}")
    print(f"   - Global rules: {len(config.global_rules)}")
    print(f"   - Column rules: {len(config.column_rules)}")
    print()
    
    # 2. Initialize validation engine
    print("2. Initializing validation engine...")
    engine = ValidationEngine(config)
    print(f"   - Engine initialized with log level: {config.system.log_level}")
    print()
    
    # 3. Register components (using mock components for demo)
    print("3. Registering validation components...")
    
    # Register file readers
    csv_reader = MockFileReader()
    engine.register_reader("csv", csv_reader)
    print("   - Registered CSV file reader")
    
    # Register validators
    mock_validator = MockValidator()
    engine.register_validator("mock", mock_validator)
    print("   - Registered mock validator")
    
    # Register error reporters
    console_reporter = MockErrorReporter()
    engine.register_reporter("console", console_reporter)
    print("   - Registered console error reporter")
    print()
    
    # 4. Show system information
    print("4. System information:")
    system_info = engine.get_system_info()
    print(f"   - Registered validators: {system_info['registered_validators']}")
    print(f"   - Registered readers: {system_info['registered_readers']}")
    print(f"   - Registered reporters: {system_info['registered_reporters']}")
    print(f"   - Memory usage: {system_info['memory_usage_mb']:.1f} MB")
    print()
    
    # 5. Demonstrate configuration serialization
    print("5. Configuration serialization:")
    config_dict = config.to_dict()
    print("   - Configuration converted to dictionary")
    
    # Save to JSON file
    config_file = Path("temp/sample_validation_config.json")
    config_file.parent.mkdir(exist_ok=True)
    
    with open(config_file, 'w') as f:
        json.dump(config_dict, f, indent=2)
    print(f"   - Configuration saved to: {config_file}")
    
    # Load from JSON file
    with open(config_file, 'r') as f:
        loaded_config_dict = json.load(f)
    
    restored_config = ValidationConfig.from_dict(loaded_config_dict)
    print("   - Configuration loaded and restored from file")
    print(f"   - Restored config has {len(restored_config.global_rules)} global rules")
    print()
    
    # 6. Demonstrate rule matching
    print("6. Validation rule matching:")
    test_columns = ["name", "age", "email", "phone", "address"]
    
    for column in test_columns:
        rules = config.get_rules_for_column(column)
        print(f"   - Column '{column}': {len(rules)} applicable rules")
        for rule in rules:
            print(f"     * {rule.validator_type} (priority: {rule.priority})")
    print()
    
    # 7. Show configuration details
    print("7. Configuration details:")
    print(f"   - File processing batch size: {config.file_processing.batch_size}")
    print(f"   - Parallel processing: {config.file_processing.parallel_processing}")
    print(f"   - Memory limit: {config.file_processing.memory_limit_mb} MB")
    print(f"   - Max errors per file: {config.error_reporting.max_errors_per_file}")
    print(f"   - QR3D integration: {config.system.qr3d_integration}")
    print()
    
    print("=== Demo completed successfully! ===")


def demonstrate_error_handling():
    """Demonstrate error handling and validation results."""
    print("\n=== Error Handling Demo ===\n")
    
    from all.data_verification.core.models import ValidationError, ValidationResult
    
    # Create sample validation errors
    errors = [
        ValidationError(
            file_path="sample.csv",
            row_number=1,
            column_name="age",
            error_type="range_error",
            error_message="Age value 150 is out of valid range (0-120)",
            actual_value=150,
            expected_format="0-120",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.RANGE_VALIDATION
        ),
        ValidationError(
            file_path="sample.csv",
            row_number=2,
            column_name="email",
            error_type="format_error",
            error_message="Email format is invalid",
            actual_value="invalid-email",
            expected_format="<EMAIL>",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.FORMAT_VALIDATION
        ),
        ValidationError(
            file_path="sample.csv",
            row_number=3,
            column_name="name",
            error_type="format_warning",
            error_message="Name appears to be in lowercase",
            actual_value="john doe",
            expected_format="John Doe",
            severity=ErrorSeverity.WARNING,
            category=ErrorCategory.FORMAT_VALIDATION
        )
    ]
    
    # Create validation result
    result = ValidationResult(
        file_path="sample.csv",
        is_valid=False,
        processed_rows=100,
        processing_time_ms=250.5,
        memory_usage_mb=12.3
    )
    
    # Add errors to result
    for error in errors:
        result.add_error(error)
    
    # Display results
    print("1. Validation Result Summary:")
    print(f"   - File: {result.file_path}")
    print(f"   - Valid: {result.is_valid}")
    print(f"   - Errors: {result.error_count}")
    print(f"   - Warnings: {result.warning_count}")
    print(f"   - Total issues: {result.total_issues}")
    print(f"   - Processed rows: {result.processed_rows}")
    print(f"   - Processing time: {result.processing_time_ms:.1f}ms")
    print(f"   - Memory usage: {result.memory_usage_mb:.1f}MB")
    print()
    
    # Show errors by category
    print("2. Errors by Category:")
    for category in ErrorCategory:
        category_errors = result.get_errors_by_category(category)
        if category_errors:
            print(f"   - {category.value}: {len(category_errors)} errors")
            for error in category_errors:
                print(f"     * Row {error.row_number}, Column '{error.column_name}': {error.error_message}")
    print()
    
    # Show errors by severity
    print("3. Issues by Severity:")
    for severity in ErrorSeverity:
        severity_errors = result.get_errors_by_severity(severity)
        if severity_errors:
            print(f"   - {severity.value.upper()}: {len(severity_errors)} issues")
    print()
    
    # Convert to JSON
    print("4. JSON Representation:")
    result_json = result.to_json()
    print("   - Result converted to JSON format")
    print(f"   - JSON length: {len(result_json)} characters")
    
    # Save JSON to file
    result_file = Path("temp/sample_validation_result.json")
    result_file.parent.mkdir(exist_ok=True)
    
    with open(result_file, 'w') as f:
        f.write(result_json)
    print(f"   - JSON saved to: {result_file}")
    print()
    
    print("=== Error Handling Demo completed! ===")


if __name__ == "__main__":
    # Run demonstrations
    demonstrate_basic_usage()
    demonstrate_error_handling()
    
    print("\n" + "="*50)
    print("Data Verification System is ready for use!")
    print("Next steps:")
    print("- Implement specific file readers (CSV, JSON, etc.)")
    print("- Implement specific validators (numeric, string, etc.)")
    print("- Implement error reporters (console, file, API)")
    print("- Create configuration management system")
    print("- Add batch processing capabilities")
    print("="*50)