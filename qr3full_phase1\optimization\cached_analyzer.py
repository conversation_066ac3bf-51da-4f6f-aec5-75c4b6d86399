import time
import sys
from pathlib import Path

# Add optimization directory to path
sys.path.insert(0, str(Path(__file__).parent))

from basic_cache_manager import get_cache_manager

class CachedAnalyzer:
    """Wrapper for existing analyzers with caching"""
    
    def __init__(self, original_analyzer):
        self.analyzer = original_analyzer
        self.cache = get_cache_manager()
        self.cache_enabled = True
    
    def analyze_file_cached(self, file_path: str, **kwargs):
        """Cached version of file analysis"""
        if not self.cache_enabled:
            return self.analyzer.analyze_file(file_path, **kwargs)
        
        # Generate cache key
        cache_key = self.cache.get_cache_key(file_path)
        
        # Try to get cached result
        cached_result = self.cache.get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result
        
        # Perform analysis and cache result
        start_time = time.time()
        result = self.analyzer.analyze_file(file_path, **kwargs)
        analysis_time = time.time() - start_time
        
        # Add timing info to result
        if isinstance(result, dict):
            result['_cache_info'] = {
                'cache_miss': True,
                'analysis_time': analysis_time,
                'cached_at': time.time()
            }
        
        # Cache the result
        self.cache.cache_result(cache_key, result)
        
        return result
    
    def get_cache_stats(self):
        """Get cache performance statistics"""
        return self.cache.get_cache_stats()
    
    def clear_cache(self):
        """Clear analysis cache"""
        return self.cache.clear_cache()

def wrap_analyzer_with_cache(analyzer):
    """Wrap any analyzer with caching capability"""
    return CachedAnalyzer(analyzer)
