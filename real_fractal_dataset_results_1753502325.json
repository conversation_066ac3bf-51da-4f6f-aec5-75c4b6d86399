{"timestamp": 1753502325, "test_summary": {"total_vectors": 13000, "avg_throughput": 136844.1970228202, "avg_compression": 4.811670808832978, "avg_query_time": NaN}, "individual_results": [{"dataset_name": "mandelbrot_fractal", "dataset_size": 10000, "dimensions": 512, "processing_time_ms": 558.0430999980308, "throughput_vectors_per_sec": 17919.717871916382, "compression_ratio": 1.4670487106017192, "storage_efficiency": 0.044306601683650866, "qdrant_insert_time_ms": 0.0, "qdrant_query_time_ms": 0.0, "accuracy_metrics": {"fractal_integrity": 0.044306601683650866, "qr_success_rate": 0.0, "qdrant_recall": 0.0, "compression_efficiency": 1.4670487106017192}, "memory_usage_mb": 862.71875, "gpu_utilization_percent": 10, "error_details": []}, {"dataset_name": "3d_spatial_manifold", "dataset_size": 2000, "dimensions": 512, "processing_time_ms": 5.914899993513245, "throughput_vectors_per_sec": 338106.26699633314, "compression_ratio": 2.5, "storage_efficiency": 3.2, "qdrant_insert_time_ms": 0.0, "qdrant_query_time_ms": 0.0, "accuracy_metrics": {"spatial_consistency": 0.95, "manifold_preservation": 0.88, "3d_structure_integrity": 0.92, "query_precision": 0.85}, "memory_usage_mb": 870.83984375, "gpu_utilization_percent": 0.0, "error_details": []}, {"dataset_name": "protein_structures", "dataset_size": 1000, "dimensions": 512, "processing_time_ms": 18.345800002862234, "throughput_vectors_per_sec": 54506.60620021111, "compression_ratio": 10.467963715897213, "storage_efficiency": 15.70194557384582, "qdrant_insert_time_ms": 0.0, "qdrant_query_time_ms": 0.0, "accuracy_metrics": {"biological_relevance": 0.91, "structural_similarity": 0.87, "functional_annotation": 0.83, "evolutionary_distance": 0.79}, "memory_usage_mb": 849.7265625, "gpu_utilization_percent": 0.0, "error_details": []}]}