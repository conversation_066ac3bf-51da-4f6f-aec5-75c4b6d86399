"""
Core data models for the Data Verification System.

This module defines the fundamental data structures used throughout the system
for representing validation results, errors, and configuration.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
import json


class ErrorSeverity(Enum):
    """Enumeration for error severity levels."""
    CRITICAL = "critical"  # Prevents further processing
    ERROR = "error"        # Data quality issue
    WARNING = "warning"    # Potential issue
    INFO = "info"         # Informational message


class ErrorCategory(Enum):
    """Enumeration for error categories."""
    DATA_TYPE = "data_type"
    RANGE_VALIDATION = "range_validation"
    FORMAT_VALIDATION = "format_validation"
    BUSINESS_RULE = "business_rule"
    SYSTEM_ERROR = "system_error"
    QR3D_COMPATIBILITY = "qr3d_compatibility"


@dataclass
class ValidationError:
    """
    Represents a validation error with full context.
    
    This class encapsulates all information about a validation error,
    including location, type, severity, and contextual information.
    """
    file_path: str
    row_number: int
    column_name: str
    error_type: str
    error_message: str
    actual_value: Any
    expected_format: str
    severity: ErrorSeverity = ErrorSeverity.ERROR
    category: ErrorCategory = ErrorCategory.DATA_TYPE
    timestamp: datetime = field(default_factory=datetime.now)
    context: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the validation error to a dictionary representation."""
        return {
            'file_path': self.file_path,
            'row_number': self.row_number,
            'column_name': self.column_name,
            'error_type': self.error_type,
            'error_message': self.error_message,
            'actual_value': str(self.actual_value),
            'expected_format': self.expected_format,
            'severity': self.severity.value,
            'category': self.category.value,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context
        }
    
    def __str__(self) -> str:
        """String representation of the validation error."""
        return (f"[{self.severity.value.upper()}] {self.file_path}:{self.row_number}:"
                f"{self.column_name} - {self.error_message}")


@dataclass
class ValidationResult:
    """
    Result of a validation operation.
    
    This class contains the complete results of validating a single file,
    including all errors, warnings, and performance metrics.
    """
    file_path: str
    is_valid: bool
    errors: List[ValidationError] = field(default_factory=list)
    warnings: List[ValidationError] = field(default_factory=list)
    processed_rows: int = 0
    processing_time_ms: float = 0.0
    memory_usage_mb: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def error_count(self) -> int:
        """Get the total number of errors."""
        return len(self.errors)
    
    @property
    def warning_count(self) -> int:
        """Get the total number of warnings."""
        return len(self.warnings)
    
    @property
    def total_issues(self) -> int:
        """Get the total number of issues (errors + warnings)."""
        return self.error_count + self.warning_count
    
    def add_error(self, error: ValidationError) -> None:
        """Add an error to the validation result."""
        if error.severity in [ErrorSeverity.ERROR, ErrorSeverity.CRITICAL]:
            self.errors.append(error)
            self.is_valid = False
        elif error.severity == ErrorSeverity.WARNING:
            self.warnings.append(error)
    
    def get_errors_by_category(self, category: ErrorCategory) -> List[ValidationError]:
        """Get all errors of a specific category."""
        return [error for error in self.errors if error.category == category]
    
    def get_errors_by_severity(self, severity: ErrorSeverity) -> List[ValidationError]:
        """Get all errors of a specific severity."""
        all_issues = self.errors + self.warnings
        return [error for error in all_issues if error.severity == severity]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the validation result to a dictionary representation."""
        return {
            'file_path': self.file_path,
            'is_valid': self.is_valid,
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'processed_rows': self.processed_rows,
            'processing_time_ms': self.processing_time_ms,
            'memory_usage_mb': self.memory_usage_mb,
            'timestamp': self.timestamp.isoformat(),
            'errors': [error.to_dict() for error in self.errors],
            'warnings': [error.to_dict() for error in self.warnings]
        }
    
    def to_json(self) -> str:
        """Convert the validation result to JSON string."""
        return json.dumps(self.to_dict(), indent=2)


@dataclass
class BatchValidationResult:
    """
    Result of a batch validation operation.
    
    This class contains the aggregated results of validating multiple files
    in a batch operation.
    """
    total_files: int
    successful_files: int
    failed_files: int
    total_errors: int
    total_warnings: int
    processing_time_ms: float
    results: List[ValidationResult] = field(default_factory=list)
    summary_report: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def success_rate(self) -> float:
        """Calculate the success rate as a percentage."""
        if self.total_files == 0:
            return 0.0
        return (self.successful_files / self.total_files) * 100
    
    def add_result(self, result: ValidationResult) -> None:
        """Add a validation result to the batch result."""
        self.results.append(result)
        if result.is_valid:
            self.successful_files += 1
        else:
            self.failed_files += 1
        self.total_errors += result.error_count
        self.total_warnings += result.warning_count
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the batch validation result to a dictionary representation."""
        return {
            'total_files': self.total_files,
            'successful_files': self.successful_files,
            'failed_files': self.failed_files,
            'success_rate': self.success_rate,
            'total_errors': self.total_errors,
            'total_warnings': self.total_warnings,
            'processing_time_ms': self.processing_time_ms,
            'timestamp': self.timestamp.isoformat(),
            'summary_report': self.summary_report,
            'results': [result.to_dict() for result in self.results]
        }


@dataclass
class ValidationRule:
    """
    Configuration for a single validation rule.
    
    This class defines the structure for configuring individual validation rules
    that can be applied to data columns.
    """
    validator_type: str
    column_pattern: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    priority: int = 100
    enabled: bool = True
    error_message_template: str = "Validation failed for {column_name}: {error_details}"
    
    def matches_column(self, column_name: str) -> bool:
        """Check if this rule applies to the given column name."""
        import re
        try:
            # Use fullmatch for exact matching unless pattern contains regex metacharacters
            if any(char in self.column_pattern for char in r'.*+?^${}[]|()\\'): 
                return bool(re.match(self.column_pattern, column_name))
            else:
                # For simple patterns, use exact string match
                return self.column_pattern == column_name
        except re.error:
            # If pattern is invalid, treat as literal string match
            return self.column_pattern == column_name
    
    def format_error_message(self, column_name: str, error_details: str) -> str:
        """Format the error message using the template."""
        return self.error_message_template.format(
            column_name=column_name,
            error_details=error_details
        )


@dataclass
class FileProcessingConfig:
    """Configuration for file processing behavior."""
    batch_size: int = 1000
    parallel_processing: bool = True
    streaming_mode: bool = False
    memory_limit_mb: int = 512
    timeout_seconds: int = 300
    retry_attempts: int = 3
    skip_header_rows: int = 1
    encoding: str = 'utf-8'


@dataclass
class ErrorReportingConfig:
    """Configuration for error reporting."""
    console_output: bool = True
    file_output: bool = True
    output_directory: str = "validation_reports"
    report_format: str = "json"  # json, csv, html
    include_context: bool = True
    group_similar_errors: bool = True
    max_errors_per_file: int = 1000
    include_warnings: bool = True


@dataclass
class SystemConfig:
    """System-wide configuration for the validation system."""
    max_parallel_files: int = 4
    memory_limit_gb: int = 8
    streaming_threshold_mb: int = 100
    enable_gpu_acceleration: bool = False
    monitoring_integration: bool = True
    qr3d_integration: bool = True
    log_level: str = "INFO"
    temp_directory: str = "temp"


@dataclass
class ValidationConfig:
    """
    Main configuration class for the validation system.
    
    This class combines all configuration aspects into a single structure
    that can be loaded from configuration files.
    """
    file_patterns: List[str] = field(default_factory=list)
    column_rules: Dict[str, List[ValidationRule]] = field(default_factory=dict)
    global_rules: List[ValidationRule] = field(default_factory=list)
    file_processing: FileProcessingConfig = field(default_factory=FileProcessingConfig)
    error_reporting: ErrorReportingConfig = field(default_factory=ErrorReportingConfig)
    system: SystemConfig = field(default_factory=SystemConfig)
    
    def get_rules_for_column(self, column_name: str) -> List[ValidationRule]:
        """Get all validation rules that apply to a specific column."""
        applicable_rules = []
        
        # Add global rules
        applicable_rules.extend([rule for rule in self.global_rules if rule.enabled])
        
        # Add column-specific rules
        for pattern, rules in self.column_rules.items():
            for rule in rules:
                if rule.enabled and rule.matches_column(column_name):
                    applicable_rules.append(rule)
        
        # Sort by priority (lower number = higher priority)
        applicable_rules.sort(key=lambda x: x.priority)
        return applicable_rules
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the configuration to a dictionary representation."""
        return {
            'file_patterns': self.file_patterns,
            'column_rules': {
                pattern: [
                    {
                        'validator_type': rule.validator_type,
                        'column_pattern': rule.column_pattern,
                        'parameters': rule.parameters,
                        'priority': rule.priority,
                        'enabled': rule.enabled,
                        'error_message_template': rule.error_message_template
                    }
                    for rule in rules
                ]
                for pattern, rules in self.column_rules.items()
            },
            'global_rules': [
                {
                    'validator_type': rule.validator_type,
                    'column_pattern': rule.column_pattern,
                    'parameters': rule.parameters,
                    'priority': rule.priority,
                    'enabled': rule.enabled,
                    'error_message_template': rule.error_message_template
                }
                for rule in self.global_rules
            ],
            'file_processing': {
                'batch_size': self.file_processing.batch_size,
                'parallel_processing': self.file_processing.parallel_processing,
                'streaming_mode': self.file_processing.streaming_mode,
                'memory_limit_mb': self.file_processing.memory_limit_mb,
                'timeout_seconds': self.file_processing.timeout_seconds,
                'retry_attempts': self.file_processing.retry_attempts,
                'skip_header_rows': self.file_processing.skip_header_rows,
                'encoding': self.file_processing.encoding
            },
            'error_reporting': {
                'console_output': self.error_reporting.console_output,
                'file_output': self.error_reporting.file_output,
                'output_directory': self.error_reporting.output_directory,
                'report_format': self.error_reporting.report_format,
                'include_context': self.error_reporting.include_context,
                'group_similar_errors': self.error_reporting.group_similar_errors,
                'max_errors_per_file': self.error_reporting.max_errors_per_file,
                'include_warnings': self.error_reporting.include_warnings
            },
            'system': {
                'max_parallel_files': self.system.max_parallel_files,
                'memory_limit_gb': self.system.memory_limit_gb,
                'streaming_threshold_mb': self.system.streaming_threshold_mb,
                'enable_gpu_acceleration': self.system.enable_gpu_acceleration,
                'monitoring_integration': self.system.monitoring_integration,
                'qr3d_integration': self.system.qr3d_integration,
                'log_level': self.system.log_level,
                'temp_directory': self.system.temp_directory
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationConfig':
        """Create a ValidationConfig instance from a dictionary."""
        config = cls()
        
        # Load basic fields
        config.file_patterns = data.get('file_patterns', [])
        
        # Load column rules
        column_rules_data = data.get('column_rules', {})
        config.column_rules = {}
        for pattern, rules_data in column_rules_data.items():
            config.column_rules[pattern] = [
                ValidationRule(
                    validator_type=rule_data['validator_type'],
                    column_pattern=rule_data['column_pattern'],
                    parameters=rule_data.get('parameters', {}),
                    priority=rule_data.get('priority', 100),
                    enabled=rule_data.get('enabled', True),
                    error_message_template=rule_data.get(
                        'error_message_template',
                        "Validation failed for {column_name}: {error_details}"
                    )
                )
                for rule_data in rules_data
            ]
        
        # Load global rules
        global_rules_data = data.get('global_rules', [])
        config.global_rules = [
            ValidationRule(
                validator_type=rule_data['validator_type'],
                column_pattern=rule_data['column_pattern'],
                parameters=rule_data.get('parameters', {}),
                priority=rule_data.get('priority', 100),
                enabled=rule_data.get('enabled', True),
                error_message_template=rule_data.get(
                    'error_message_template',
                    "Validation failed for {column_name}: {error_details}"
                )
            )
            for rule_data in global_rules_data
        ]
        
        # Load nested configurations
        if 'file_processing' in data:
            fp_data = data['file_processing']
            config.file_processing = FileProcessingConfig(
                batch_size=fp_data.get('batch_size', 1000),
                parallel_processing=fp_data.get('parallel_processing', True),
                streaming_mode=fp_data.get('streaming_mode', False),
                memory_limit_mb=fp_data.get('memory_limit_mb', 512),
                timeout_seconds=fp_data.get('timeout_seconds', 300),
                retry_attempts=fp_data.get('retry_attempts', 3),
                skip_header_rows=fp_data.get('skip_header_rows', 1),
                encoding=fp_data.get('encoding', 'utf-8')
            )
        
        if 'error_reporting' in data:
            er_data = data['error_reporting']
            config.error_reporting = ErrorReportingConfig(
                console_output=er_data.get('console_output', True),
                file_output=er_data.get('file_output', True),
                output_directory=er_data.get('output_directory', 'validation_reports'),
                report_format=er_data.get('report_format', 'json'),
                include_context=er_data.get('include_context', True),
                group_similar_errors=er_data.get('group_similar_errors', True),
                max_errors_per_file=er_data.get('max_errors_per_file', 1000),
                include_warnings=er_data.get('include_warnings', True)
            )
        
        if 'system' in data:
            sys_data = data['system']
            config.system = SystemConfig(
                max_parallel_files=sys_data.get('max_parallel_files', 4),
                memory_limit_gb=sys_data.get('memory_limit_gb', 8),
                streaming_threshold_mb=sys_data.get('streaming_threshold_mb', 100),
                enable_gpu_acceleration=sys_data.get('enable_gpu_acceleration', False),
                monitoring_integration=sys_data.get('monitoring_integration', True),
                qr3d_integration=sys_data.get('qr3d_integration', True),
                log_level=sys_data.get('log_level', 'INFO'),
                temp_directory=sys_data.get('temp_directory', 'temp')
            )
        
        return config