"""
Unit tests for core interfaces and engine.

This module tests the abstract base classes and the ValidationEngine
to ensure they work correctly together.
"""

import unittest
from unittest.mock import Mock, patch
from typing import Any, Dict, Iterator, List

from all.data_verification.core.interfaces import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er
from all.data_verification.core.models import (
    ValidationConfig, ValidationResult, ValidationError, ErrorSeverity
)
from all.data_verification.core.engine import ValidationEngine


class MockFileReader(FileReader):
    """Mock file reader for testing."""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.test_data = [
            {"name": "<PERSON>", "age": "25", "email": "<EMAIL>"},
            {"name": "<PERSON>", "age": "30", "email": "<EMAIL>"},
            {"name": "Bob", "age": "invalid", "email": "<EMAIL>"}
        ]
        self.test_headers = ["name", "age", "email"]
    
    def read(self, file_path: str) -> Iterator[Dict[str, Any]]:
        """Mock file reading."""
        for row in self.test_data:
            yield row
    
    def get_headers(self, file_path: str) -> List[str]:
        """Mock header reading."""
        return self.test_headers
    
    def validate_file_format(self, file_path: str) -> bool:
        """Mock format validation."""
        return file_path.endswith('.csv')
    
    def get_row_count(self, file_path: str) -> int:
        """Mock row count."""
        return len(self.test_data)


class MockValidator(Validator):
    """Mock validator for testing."""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.should_fail = config.get('should_fail', False) if config else False
    
    def validate(self, value: Any, context: Dict[str, Any]) -> ValidationResult:
        """Mock validation."""
        result = ValidationResult(
            file_path=context.get('file_path', ''),
            is_valid=True
        )
        
        # Simulate validation failure for "invalid" values
        if str(value) == "invalid" or self.should_fail:
            error = ValidationError(
                file_path=context.get('file_path', ''),
                row_number=context.get('row_number', 0),
                column_name=context.get('column_name', ''),
                error_type="mock_error",
                error_message="Mock validation failed",
                actual_value=value,
                expected_format="valid value"
            )
            result.add_error(error)
        
        return result
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Mock config schema."""
        return {
            "type": "object",
            "properties": {
                "should_fail": {"type": "boolean"}
            }
        }
    
    def get_supported_types(self) -> List[str]:
        """Mock supported types."""
        return ["string", "number"]


class MockErrorReporter(ErrorReporter):
    """Mock error reporter for testing."""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.reported_errors = []
        self.reported_results = []
        self.initialized = False
        self.finalized = False
    
    def report_error(self, error: ValidationError) -> None:
        """Mock error reporting."""
        self.reported_errors.append(error)
    
    def report_result(self, result: ValidationResult) -> None:
        """Mock result reporting."""
        self.reported_results.append(result)
    
    def generate_summary(self, errors: List[ValidationError]) -> str:
        """Mock summary generation."""
        return f"Mock summary: {len(errors)} errors"
    
    def initialize(self) -> None:
        """Mock initialization."""
        self.initialized = True
    
    def finalize(self) -> None:
        """Mock finalization."""
        self.finalized = True


class TestCoreInterfaces(unittest.TestCase):
    """Test cases for core interfaces."""
    
    def test_file_reader_interface(self):
        """Test FileReader interface implementation."""
        reader = MockFileReader()
        
        # Test file format validation
        self.assertTrue(reader.validate_file_format("test.csv"))
        self.assertFalse(reader.validate_file_format("test.txt"))
        
        # Test header reading
        headers = reader.get_headers("test.csv")
        self.assertEqual(headers, ["name", "age", "email"])
        
        # Test row count
        row_count = reader.get_row_count("test.csv")
        self.assertEqual(row_count, 3)
        
        # Test data reading
        data = list(reader.read("test.csv"))
        self.assertEqual(len(data), 3)
        self.assertEqual(data[0]["name"], "John")
        self.assertEqual(data[2]["age"], "invalid")
    
    def test_validator_interface(self):
        """Test Validator interface implementation."""
        validator = MockValidator()
        
        # Test supported types
        types = validator.get_supported_types()
        self.assertIn("string", types)
        self.assertIn("number", types)
        
        # Test config schema
        schema = validator.get_config_schema()
        self.assertIn("properties", schema)
        
        # Test successful validation
        context = {
            'file_path': 'test.csv',
            'row_number': 1,
            'column_name': 'name'
        }
        result = validator.validate("John", context)
        self.assertTrue(result.is_valid)
        self.assertEqual(result.error_count, 0)
        
        # Test failed validation
        result = validator.validate("invalid", context)
        self.assertFalse(result.is_valid)
        self.assertEqual(result.error_count, 1)
        self.assertEqual(result.errors[0].error_message, "Mock validation failed")
    
    def test_error_reporter_interface(self):
        """Test ErrorReporter interface implementation."""
        reporter = MockErrorReporter()
        
        # Test initialization
        reporter.initialize()
        self.assertTrue(reporter.initialized)
        
        # Test error reporting
        error = ValidationError(
            file_path="test.csv",
            row_number=1,
            column_name="age",
            error_type="test_error",
            error_message="Test error",
            actual_value="invalid",
            expected_format="valid"
        )
        reporter.report_error(error)
        self.assertEqual(len(reporter.reported_errors), 1)
        self.assertEqual(reporter.reported_errors[0].error_message, "Test error")
        
        # Test result reporting
        result = ValidationResult(file_path="test.csv", is_valid=False)
        result.add_error(error)
        reporter.report_result(result)
        self.assertEqual(len(reporter.reported_results), 1)
        
        # Test summary generation
        summary = reporter.generate_summary([error])
        self.assertIn("1 errors", summary)
        
        # Test finalization
        reporter.finalize()
        self.assertTrue(reporter.finalized)


class TestValidationEngine(unittest.TestCase):
    """Test cases for ValidationEngine."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = ValidationConfig()
        self.engine = ValidationEngine(self.config)
        
        # Register mock components
        self.mock_reader = MockFileReader()
        self.mock_validator = MockValidator()
        self.mock_reporter = MockErrorReporter()
        
        self.engine.register_reader("csv", self.mock_reader)
        self.engine.register_validator("mock", self.mock_validator)
        self.engine.register_reporter("mock", self.mock_reporter)
    
    def test_engine_initialization(self):
        """Test ValidationEngine initialization."""
        self.assertIsInstance(self.engine.config, ValidationConfig)
        self.assertIsNotNone(self.engine.logger)
        self.assertEqual(len(self.engine.validator_registry), 1)
        self.assertEqual(len(self.engine.reader_registry), 1)
        self.assertEqual(len(self.engine.reporter_registry), 1)
    
    def test_component_registration(self):
        """Test component registration."""
        # Test validator registration
        new_validator = MockValidator({"should_fail": True})
        self.engine.register_validator("failing_mock", new_validator)
        self.assertIn("failing_mock", self.engine.validator_registry)
        
        # Test reader registration
        new_reader = MockFileReader()
        self.engine.register_reader("json", new_reader)
        self.assertIn("json", self.engine.reader_registry)
        
        # Test reporter registration
        new_reporter = MockErrorReporter()
        self.engine.register_reporter("file", new_reporter)
        self.assertIn("file", self.engine.reporter_registry)
    
    @patch('all.data_verification.core.engine.ValidationEngine._get_file_reader')
    @patch('all.data_verification.core.engine.ValidationEngine._validate_column_value')
    def test_validate_file_success(self, mock_validate_column, mock_get_reader):
        """Test successful file validation."""
        # Setup mocks
        mock_get_reader.return_value = self.mock_reader
        mock_validate_column.return_value = []  # No errors
        
        # Test validation
        result = self.engine.validate_file("test.csv")
        
        self.assertIsInstance(result, ValidationResult)
        self.assertEqual(result.file_path, "test.csv")
        self.assertTrue(result.is_valid)
        self.assertEqual(result.processed_rows, 3)
        self.assertGreaterEqual(result.processing_time_ms, 0)
    
    @patch('all.data_verification.core.engine.ValidationEngine._get_file_reader')
    @patch('all.data_verification.core.engine.ValidationEngine._validate_column_value')
    def test_validate_file_with_errors(self, mock_validate_column, mock_get_reader):
        """Test file validation with errors."""
        # Setup mocks
        mock_get_reader.return_value = self.mock_reader
        
        # Mock validation to return errors for "invalid" values
        def mock_validation(file_path, row_number, column_name, value, row_data):
            if str(value) == "invalid":
                error = ValidationError(
                    file_path=file_path,
                    row_number=row_number,
                    column_name=column_name,
                    error_type="mock_error",
                    error_message="Mock validation failed",
                    actual_value=value,
                    expected_format="valid value"
                )
                return [error]
            return []
        
        mock_validate_column.side_effect = mock_validation
        
        # Test validation
        result = self.engine.validate_file("test.csv")
        
        self.assertIsInstance(result, ValidationResult)
        self.assertFalse(result.is_valid)
        self.assertGreater(result.error_count, 0)
        self.assertEqual(result.processed_rows, 3)
    
    def test_validate_batch(self):
        """Test batch validation."""
        with patch.object(self.engine, 'validate_file') as mock_validate:
            # Setup mock to return different results for different files
            def mock_validation(file_path):
                if "fail" in file_path:
                    result = ValidationResult(file_path=file_path, is_valid=False)
                    error = ValidationError(
                        file_path=file_path, row_number=1, column_name="test",
                        error_type="test", error_message="Test error",
                        actual_value="", expected_format=""
                    )
                    result.add_error(error)
                    return result
                else:
                    return ValidationResult(file_path=file_path, is_valid=True)
            
            mock_validate.side_effect = mock_validation
            
            # Test batch validation
            file_paths = ["test1.csv", "test_fail.csv", "test2.csv"]
            batch_result = self.engine.validate_batch(file_paths)
            
            self.assertEqual(batch_result.total_files, 3)
            self.assertEqual(batch_result.successful_files, 2)
            self.assertEqual(batch_result.failed_files, 1)
            self.assertEqual(batch_result.total_errors, 1)
            self.assertGreater(batch_result.processing_time_ms, 0)
            self.assertIn("Batch Validation Summary", batch_result.summary_report)
    
    def test_get_system_info(self):
        """Test system information retrieval."""
        info = self.engine.get_system_info()
        
        self.assertIn('config', info)
        self.assertIn('registered_validators', info)
        self.assertIn('registered_readers', info)
        self.assertIn('registered_reporters', info)
        self.assertIn('memory_usage_mb', info)
        
        self.assertEqual(info['registered_validators'], ['mock'])
        self.assertEqual(info['registered_readers'], ['csv'])
        self.assertEqual(info['registered_reporters'], ['mock'])


if __name__ == '__main__':
    unittest.main()