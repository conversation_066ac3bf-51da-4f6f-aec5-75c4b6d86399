"""
Validation Engine - Central orchestrator for the Data Verification System.

This module contains the main ValidationEngine class that coordinates
file reading, validation, and error reporting operations.
"""

import logging
import time
import traceback
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import psutil
import os

from .interfaces import FileReader, Validator, ErrorReporter, ValidatorFactory, FileReaderFactory, ErrorReporterFactory
from .models import (
    ValidationConfig, ValidationResult, ValidationError, BatchValidationResult,
    ErrorSeverity, ErrorCategory
)


class ValidationEngine:
    """
    Central orchestrator for the data validation system.
    
    The ValidationEngine coordinates file reading, validation rule application,
    and error reporting. It supports both single-file and batch processing
    with configurable parallelization and error handling.
    """
    
    def __init__(self, config: ValidationConfig):
        """
        Initialize the validation engine with configuration.
        
        Args:
            config: ValidationConfig instance containing system configuration
        """
        self.config = config
        self.logger = self._setup_logging()
        
        # Component registries
        self.validator_registry: Dict[str, Validator] = {}
        self.reader_registry: Dict[str, FileReader] = {}
        self.reporter_registry: Dict[str, ErrorReporter] = {}
        
        # Factories for dynamic component creation
        self.validator_factory: Optional[ValidatorFactory] = None
        self.reader_factory: Optional[FileReaderFactory] = None
        self.reporter_factory: Optional[ErrorReporterFactory] = None
        
        # Performance monitoring
        self.process = psutil.Process()
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        self.logger.info(f"ValidationEngine initialized with config: {config.system.log_level}")
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logger = logging.getLogger('data_verification')
        logger.setLevel(getattr(logging, self.config.system.log_level.upper()))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def register_validator(self, name: str, validator: Validator) -> None:
        """
        Register a validator with the engine.
        
        Args:
            name: Name to register the validator under
            validator: Validator instance to register
        """
        self.validator_registry[name] = validator
        self.logger.debug(f"Registered validator: {name}")
    
    def register_reader(self, name: str, reader: FileReader) -> None:
        """
        Register a file reader with the engine.
        
        Args:
            name: Name to register the reader under
            reader: FileReader instance to register
        """
        self.reader_registry[name] = reader
        self.logger.debug(f"Registered file reader: {name}")
    
    def register_reporter(self, name: str, reporter: ErrorReporter) -> None:
        """
        Register an error reporter with the engine.
        
        Args:
            name: Name to register the reporter under
            reporter: ErrorReporter instance to register
        """
        self.reporter_registry[name] = reporter
        self.logger.debug(f"Registered error reporter: {name}")
    
    def set_validator_factory(self, factory: ValidatorFactory) -> None:
        """Set the validator factory for dynamic validator creation."""
        self.validator_factory = factory
    
    def set_reader_factory(self, factory: FileReaderFactory) -> None:
        """Set the file reader factory for dynamic reader creation."""
        self.reader_factory = factory
    
    def set_reporter_factory(self, factory: ErrorReporterFactory) -> None:
        """Set the error reporter factory for dynamic reporter creation."""
        self.reporter_factory = factory
    
    def validate_file(self, file_path: str, reader: Optional[FileReader] = None) -> ValidationResult:
        """
        Validate a single file.
        
        Args:
            file_path: Path to the file to validate
            reader: Optional specific file reader to use
            
        Returns:
            ValidationResult: Complete validation results for the file
        """
        start_time = time.time()
        start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        result = ValidationResult(
            file_path=file_path,
            is_valid=True
        )
        
        try:
            self.logger.info(f"Starting validation of file: {file_path}")
            
            # Get or create file reader
            if reader is None:
                reader = self._get_file_reader(file_path)
            
            # Validate file format
            if not reader.validate_file_format(file_path):
                error = ValidationError(
                    file_path=file_path,
                    row_number=0,
                    column_name="",
                    error_type="format_error",
                    error_message="File format is not supported or file is corrupted",
                    actual_value="",
                    expected_format="Valid file format",
                    severity=ErrorSeverity.CRITICAL,
                    category=ErrorCategory.SYSTEM_ERROR
                )
                result.add_error(error)
                return result
            
            # Get file headers for validation rule matching
            try:
                headers = reader.get_headers(file_path)
                self.logger.debug(f"File headers: {headers}")
            except Exception as e:
                error = ValidationError(
                    file_path=file_path,
                    row_number=0,
                    column_name="",
                    error_type="header_error",
                    error_message=f"Could not read file headers: {str(e)}",
                    actual_value="",
                    expected_format="Readable headers",
                    severity=ErrorSeverity.CRITICAL,
                    category=ErrorCategory.SYSTEM_ERROR
                )
                result.add_error(error)
                return result
            
            # Process file row by row
            row_number = 0
            error_count = 0
            max_errors = self.config.error_reporting.max_errors_per_file
            
            for row_data in reader.read(file_path):
                row_number += 1
                
                # Check memory usage
                current_memory = self.process.memory_info().rss / 1024 / 1024
                if current_memory > self.config.system.memory_limit_gb * 1024:
                    self.logger.warning(f"Memory usage ({current_memory:.1f}MB) exceeds limit")
                    if self.config.file_processing.streaming_mode:
                        # In streaming mode, continue but log warning
                        pass
                    else:
                        # Stop processing if not in streaming mode
                        error = ValidationError(
                            file_path=file_path,
                            row_number=row_number,
                            column_name="",
                            error_type="memory_limit",
                            error_message=f"Memory usage exceeded limit ({current_memory:.1f}MB)",
                            actual_value=current_memory,
                            expected_format=f"<{self.config.system.memory_limit_gb * 1024}MB",
                            severity=ErrorSeverity.CRITICAL,
                            category=ErrorCategory.SYSTEM_ERROR
                        )
                        result.add_error(error)
                        break
                
                # Validate each column in the row
                for column_name, value in row_data.items():
                    if column_name in headers:
                        column_errors = self._validate_column_value(
                            file_path, row_number, column_name, value, row_data
                        )
                        
                        for error in column_errors:
                            result.add_error(error)
                            error_count += 1
                            
                            # Stop if we've hit the error limit
                            if error_count >= max_errors:
                                self.logger.warning(f"Reached maximum error limit ({max_errors}) for file")
                                break
                
                if error_count >= max_errors:
                    break
            
            result.processed_rows = row_number
            
        except Exception as e:
            self.logger.error(f"Unexpected error validating file {file_path}: {str(e)}")
            self.logger.debug(traceback.format_exc())
            
            error = ValidationError(
                file_path=file_path,
                row_number=0,
                column_name="",
                error_type="system_error",
                error_message=f"Unexpected error during validation: {str(e)}",
                actual_value="",
                expected_format="Successful validation",
                severity=ErrorSeverity.CRITICAL,
                category=ErrorCategory.SYSTEM_ERROR
            )
            result.add_error(error)
        
        # Calculate performance metrics
        end_time = time.time()
        end_memory = self.process.memory_info().rss / 1024 / 1024
        
        result.processing_time_ms = (end_time - start_time) * 1000
        result.memory_usage_mb = end_memory - start_memory
        
        self.logger.info(
            f"Validation completed for {file_path}: "
            f"{result.error_count} errors, {result.warning_count} warnings, "
            f"{result.processed_rows} rows processed in {result.processing_time_ms:.1f}ms"
        )
        
        return result
    
    def validate_batch(self, file_paths: List[str]) -> BatchValidationResult:
        """
        Validate multiple files in batch.
        
        Args:
            file_paths: List of file paths to validate
            
        Returns:
            BatchValidationResult: Aggregated results from all files
        """
        start_time = time.time()
        
        batch_result = BatchValidationResult(
            total_files=len(file_paths),
            successful_files=0,
            failed_files=0,
            total_errors=0,
            total_warnings=0,
            processing_time_ms=0.0
        )
        
        self.logger.info(f"Starting batch validation of {len(file_paths)} files")
        
        if self.config.file_processing.parallel_processing and len(file_paths) > 1:
            # Parallel processing
            max_workers = min(self.config.system.max_parallel_files, len(file_paths))
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all validation tasks
                future_to_path = {
                    executor.submit(self.validate_file, file_path): file_path
                    for file_path in file_paths
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_path):
                    file_path = future_to_path[future]
                    try:
                        result = future.result()
                        batch_result.add_result(result)
                        self.logger.debug(f"Completed validation of {file_path}")
                    except Exception as e:
                        self.logger.error(f"Error validating {file_path}: {str(e)}")
                        # Create error result for failed file
                        error_result = ValidationResult(
                            file_path=file_path,
                            is_valid=False
                        )
                        error = ValidationError(
                            file_path=file_path,
                            row_number=0,
                            column_name="",
                            error_type="batch_processing_error",
                            error_message=f"Failed to process file in batch: {str(e)}",
                            actual_value="",
                            expected_format="Successful processing",
                            severity=ErrorSeverity.CRITICAL,
                            category=ErrorCategory.SYSTEM_ERROR
                        )
                        error_result.add_error(error)
                        batch_result.add_result(error_result)
        else:
            # Sequential processing
            for file_path in file_paths:
                try:
                    result = self.validate_file(file_path)
                    batch_result.add_result(result)
                except Exception as e:
                    self.logger.error(f"Error validating {file_path}: {str(e)}")
                    # Create error result for failed file
                    error_result = ValidationResult(
                        file_path=file_path,
                        is_valid=False
                    )
                    error = ValidationError(
                        file_path=file_path,
                        row_number=0,
                        column_name="",
                        error_type="processing_error",
                        error_message=f"Failed to process file: {str(e)}",
                        actual_value="",
                        expected_format="Successful processing",
                        severity=ErrorSeverity.CRITICAL,
                        category=ErrorCategory.SYSTEM_ERROR
                    )
                    error_result.add_error(error)
                    batch_result.add_result(error_result)
        
        # Calculate total processing time
        end_time = time.time()
        batch_result.processing_time_ms = (end_time - start_time) * 1000
        
        # Generate summary report
        batch_result.summary_report = self._generate_batch_summary(batch_result)
        
        self.logger.info(
            f"Batch validation completed: {batch_result.successful_files}/{batch_result.total_files} "
            f"files successful, {batch_result.total_errors} total errors, "
            f"{batch_result.total_warnings} total warnings in {batch_result.processing_time_ms:.1f}ms"
        )
        
        return batch_result
    
    def _get_file_reader(self, file_path: str) -> FileReader:
        """
        Get appropriate file reader for the given file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            FileReader: Appropriate reader for the file type
            
        Raises:
            ValueError: If no suitable reader is found
        """
        file_extension = Path(file_path).suffix.lower()
        
        # Try to use factory first
        if self.reader_factory:
            try:
                return self.reader_factory.create_reader(file_path, self.config.to_dict())
            except ValueError:
                pass  # Fall back to registry lookup
        
        # Look for registered reader by extension
        reader_name = file_extension.lstrip('.')
        if reader_name in self.reader_registry:
            return self.reader_registry[reader_name]
        
        # Default fallback - this would need to be implemented
        raise ValueError(f"No file reader available for file type: {file_extension}")
    
    def _validate_column_value(self, file_path: str, row_number: int, 
                             column_name: str, value: Any, row_data: Dict[str, Any]) -> List[ValidationError]:
        """
        Validate a single column value against all applicable rules.
        
        Args:
            file_path: Path to the file being processed
            row_number: Current row number
            column_name: Name of the column being validated
            value: Value to validate
            row_data: Complete row data for context
            
        Returns:
            List[ValidationError]: List of validation errors found
        """
        errors = []
        
        # Get validation rules for this column
        rules = self.config.get_rules_for_column(column_name)
        
        # Create validation context
        context = {
            'file_path': file_path,
            'row_number': row_number,
            'column_name': column_name,
            'row_data': row_data
        }
        
        # Apply each validation rule
        for rule in rules:
            try:
                # Get or create validator
                validator = self._get_validator(rule.validator_type, rule.parameters)
                
                # Perform validation
                validation_result = validator.validate(value, context)
                
                # Add any errors from this validation
                errors.extend(validation_result.errors)
                
            except Exception as e:
                self.logger.error(f"Error applying validation rule {rule.validator_type}: {str(e)}")
                
                # Create error for validation rule failure
                error = ValidationError(
                    file_path=file_path,
                    row_number=row_number,
                    column_name=column_name,
                    error_type="validator_error",
                    error_message=f"Validation rule '{rule.validator_type}' failed: {str(e)}",
                    actual_value=value,
                    expected_format="Valid validation rule execution",
                    severity=ErrorSeverity.ERROR,
                    category=ErrorCategory.SYSTEM_ERROR
                )
                errors.append(error)
        
        return errors
    
    def _get_validator(self, validator_type: str, parameters: Dict[str, Any]) -> Validator:
        """
        Get validator instance for the specified type and parameters.
        
        Args:
            validator_type: Type of validator to get
            parameters: Parameters for the validator
            
        Returns:
            Validator: Configured validator instance
            
        Raises:
            ValueError: If validator type is not supported
        """
        # Try to use factory first
        if self.validator_factory:
            try:
                return self.validator_factory.create_validator(validator_type, parameters)
            except ValueError:
                pass  # Fall back to registry lookup
        
        # Look for registered validator
        if validator_type in self.validator_registry:
            return self.validator_registry[validator_type]
        
        raise ValueError(f"No validator available for type: {validator_type}")
    
    def _generate_batch_summary(self, batch_result: BatchValidationResult) -> str:
        """
        Generate a summary report for batch validation results.
        
        Args:
            batch_result: Batch validation results to summarize
            
        Returns:
            str: Formatted summary report
        """
        summary_lines = [
            "=== Batch Validation Summary ===",
            f"Total Files: {batch_result.total_files}",
            f"Successful: {batch_result.successful_files}",
            f"Failed: {batch_result.failed_files}",
            f"Success Rate: {batch_result.success_rate:.1f}%",
            f"Total Errors: {batch_result.total_errors}",
            f"Total Warnings: {batch_result.total_warnings}",
            f"Processing Time: {batch_result.processing_time_ms:.1f}ms",
            "",
            "=== File Results ==="
        ]
        
        for result in batch_result.results:
            status = "✓ PASS" if result.is_valid else "✗ FAIL"
            summary_lines.append(
                f"{status} {result.file_path} - "
                f"{result.error_count} errors, {result.warning_count} warnings, "
                f"{result.processed_rows} rows ({result.processing_time_ms:.1f}ms)"
            )
        
        return "\n".join(summary_lines)
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        Get information about the validation system.
        
        Returns:
            Dict[str, Any]: System information including registered components
        """
        return {
            'config': self.config.to_dict(),
            'registered_validators': list(self.validator_registry.keys()),
            'registered_readers': list(self.reader_registry.keys()),
            'registered_reporters': list(self.reporter_registry.keys()),
            'memory_usage_mb': self.process.memory_info().rss / 1024 / 1024,
            'has_validator_factory': self.validator_factory is not None,
            'has_reader_factory': self.reader_factory is not None,
            'has_reporter_factory': self.reporter_factory is not None
        }