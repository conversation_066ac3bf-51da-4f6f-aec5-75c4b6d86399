import csv

def verify_data(file_path):
    """
    Verifies that all entries in the second column of the CSV file are positive numbers.
    
    Args:
        file_path (str): The path to the CSV file to be verified.
    
    Returns:
        str: A message indicating whether the verification passed or a list of errors.
    """
    errors = []
    try:
        with open(file_path, 'r') as file:
            reader = csv.reader(file)
            headers = next(reader)  # Skip the header row
            for row_num, row in enumerate(reader, start=2):
                # Check the second column (index 1)
                value = row[1]
                try:
                    num = float(value)
                    if num <= 0:
                        errors.append(f"Row {row_num}: Non-positive number {num}")
                except ValueError:
                    errors.append(f"Row {row_num}: Invalid number format {value}")
    except FileNotFoundError:
        return "File not found"
    except IndexError:
        return "Error: CSV file has fewer columns than expected"
    except Exception as e:
        return f"Error: {str(e)}"
    
    if errors:
        return "\n".join(errors)
    else:
        return "All data verified successfully"

# Example usage
# Create a sample CSV file for testing (uncomment to use)
"""
with open('data.csv', 'w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['Name', 'Value', 'Date'])
    writer.writerow(['Item1', '5', '2023-01-01'])
    writer.writerow(['Item2', '-3', '2023-01-02'])
    writer.writerow(['Item3', 'abc', '2023-01-03'])
    writer.writerow(['Item4', '10', '2023-01-04'])
"""

result = verify_data('data.csv')
print(result)