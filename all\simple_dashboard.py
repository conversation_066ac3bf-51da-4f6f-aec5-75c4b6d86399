#!/usr/bin/env python3
"""
Simple QR3 Dashboard with File Upload
Focuses on file upload, compression, and storage functionality
"""

import dash
from dash import dcc, html, Input, Output, callback, State
import plotly.graph_objs as go
import plotly.express as px
import pandas as pd
import psutil
import time
import threading
import queue
from datetime import datetime, timedelta
import json
import requests
import logging
from typing import Dict, List, Optional, Any
import os
from dataclasses import dataclass
import numpy as np
import base64

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Dash app
app = dash.Dash(__name__)
app.title = "QR3 File Upload & Compression Dashboard"

# Define the layout with file upload focus
app.layout = html.Div([
    html.Div([
        html.H1("🚀 QR3 File Upload & Compression Dashboard", 
                className="dashboard-title"),
        html.P("Upload files, test compression, and monitor storage", 
               className="system-info"),
        html.Div(id="last-updated", className="last-updated")
    ], className="header"),
    
    # File Upload Section
    html.Div([
        html.H2("📁 File Upload & Compression", className="section-title"),
        
        html.Div([
            html.Div([
                html.H3("Upload Files"),
                dcc.Upload(
                    id='upload-data',
                    children=html.Div([
                        'Drag and Drop or ',
                        html.A('Select Files')
                    ]),
                    style={
                        'width': '100%',
                        'height': '80px',
                        'lineHeight': '80px',
                        'borderWidth': '2px',
                        'borderStyle': 'dashed',
                        'borderRadius': '10px',
                        'textAlign': 'center',
                        'margin': '10px',
                        'backgroundColor': '#2a2a2a',
                        'color': '#ffffff',
                        'fontSize': '16px'
                    },
                    multiple=True
                ),
                html.Div(id='upload-status', className="upload-status")
            ], className="upload-section", style={'width': '48%', 'display': 'inline-block'}),

            html.Div([
                html.H3("Compression Results"),
                html.Div(id="compression-results", className="compression-results")
            ], className="compression-section", style={'width': '48%', 'display': 'inline-block', 'marginLeft': '4%'})
        ], style={'display': 'flex', 'flexWrap': 'wrap'}),

        html.Div([
            html.H3("Uploaded Files"),
            html.Div(id="uploaded-files-list", className="files-list")
        ], className="files-section", style={'marginTop': '20px'})
    ], className="upload-container"),
    
    # System Status Section
    html.Div([
        html.H2("⚡ System Status", className="section-title"),
        html.Div([
            html.Div([
                html.H4("API Server"),
                html.Div(id="api-status", className="status-indicator")
            ], className="status-card"),
            
            html.Div([
                html.H4("Storage"),
                html.Div(id="storage-status", className="status-indicator")
            ], className="status-card"),
            
            html.Div([
                html.H4("Compression"),
                html.Div(id="compression-status", className="status-indicator")
            ], className="status-card")
        ], className="status-grid")
    ], className="status-container"),
    
    # Auto-refresh interval
    dcc.Interval(
        id='interval-component',
        interval=2000,  # Update every 2 seconds
        n_intervals=0
    )
    
], className="dashboard-container")

# File Upload Callbacks
@app.callback(
    [Output('upload-status', 'children'),
     Output('compression-results', 'children')],
    [Input('upload-data', 'contents')],
    [State('upload-data', 'filename')]
)
def handle_file_upload(contents, filenames):
    """Handle file upload and compression"""
    if contents is None:
        return "", ""
    
    upload_results = []
    compression_results = []
    
    for content, filename in zip(contents, filenames):
        try:
            # Decode the file content
            content_type, content_string = content.split(',')
            decoded = base64.b64decode(content_string)
            
            # Send to API for processing
            files = {'file': (filename, decoded, 'application/octet-stream')}
            response = requests.post('http://localhost:8000/api/upload', files=files, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                upload_results.append(html.Div([
                    html.Span(f"✅ {filename} uploaded successfully", 
                             style={'color': '#4CAF50', 'fontWeight': 'bold'})
                ]))
                
                space_saved = ((result['original_size'] - result['compressed_size']) / result['original_size'] * 100)
                compression_results.append(html.Div([
                    html.H5(filename, style={'color': '#ffffff', 'marginBottom': '10px'}),
                    html.P(f"📊 Original Size: {result['original_size']:,} bytes", style={'margin': '5px 0'}),
                    html.P(f"🗜️ Compressed Size: {result['compressed_size']:,} bytes", style={'margin': '5px 0'}),
                    html.P(f"📈 Compression Ratio: {result['compression_ratio']}:1", style={'margin': '5px 0'}),
                    html.P(f"🔧 Algorithm: {result.get('compression_algorithm', 'unknown')}",
                          style={'margin': '5px 0', 'color': '#2196F3'}),
                    html.P(f"⚡ Efficiency: {result.get('compression_efficiency', 0):.1f}%",
                          style={'margin': '5px 0', 'color': '#FF9800'}),
                    html.P(f"💾 Space Saved: {space_saved:.1f}%",
                          style={'margin': '5px 0', 'color': '#4CAF50', 'fontWeight': 'bold'})
                ], style={
                    'backgroundColor': '#3a3a3a',
                    'padding': '15px',
                    'margin': '10px 0',
                    'borderRadius': '8px',
                    'border': '1px solid #555'
                }))
            else:
                upload_results.append(html.Div([
                    html.Span(f"❌ Failed to upload {filename}", 
                             style={'color': '#f44336', 'fontWeight': 'bold'})
                ]))
                
        except Exception as e:
            upload_results.append(html.Div([
                html.Span(f"❌ Error uploading {filename}: {str(e)}", 
                         style={'color': '#f44336', 'fontWeight': 'bold'})
            ]))
    
    return upload_results, compression_results

@app.callback(
    [Output('uploaded-files-list', 'children'),
     Output('api-status', 'children'),
     Output('storage-status', 'children'),
     Output('compression-status', 'children'),
     Output('last-updated', 'children')],
    [Input('interval-component', 'n_intervals')]
)
def update_dashboard(n):
    """Update the dashboard with current status"""
    try:
        # Check API status
        api_response = requests.get('http://localhost:8000/health', timeout=5)
        api_status = "🟢 Online" if api_response.status_code == 200 else "🔴 Offline"
    except:
        api_status = "🔴 Offline"
    
    try:
        # Get uploaded files
        response = requests.get('http://localhost:8000/api/uploads', timeout=5)
        if response.status_code == 200:
            data = response.json()
            uploads = data.get('uploads', [])
            
            if not uploads:
                files_list = html.Div("No files uploaded yet", 
                                    style={'textAlign': 'center', 'color': '#888', 'padding': '20px'})
            else:
                file_items = []
                for upload in uploads[:10]:  # Show last 10 uploads
                    algorithm = upload.get('compression_algorithm', 'unknown')
                    efficiency = upload.get('compression_efficiency', 0)

                    file_items.append(html.Div([
                        html.Div([
                            html.Strong(upload['original_filename'], style={'color': '#ffffff'}),
                            html.Span(f" ({upload['compression_ratio']}:1)",
                                     style={'color': '#4CAF50', 'marginLeft': '10px'}),
                            html.Span(f" | {algorithm}",
                                     style={'color': '#2196F3', 'marginLeft': '10px', 'fontSize': '0.9em'})
                        ]),
                        html.Div([
                            html.Span(f"Size: {upload['original_size']:,} → {upload['compressed_size']:,} bytes"),
                            html.Span(f" | Efficiency: {efficiency:.1f}%", style={'color': '#FF9800'}),
                            html.Span(f" | {upload['timestamp'][:19]}", style={'color': '#888'})
                        ], style={'fontSize': '0.9em', 'color': '#ccc', 'marginTop': '5px'})
                    ], style={
                        'backgroundColor': '#3a3a3a',
                        'padding': '10px',
                        'margin': '5px 0',
                        'borderRadius': '5px',
                        'border': '1px solid #555'
                    }))
                
                files_list = file_items
            
            storage_status = "🟢 Working"
            compression_status = "🟢 Active"
        else:
            files_list = html.Div("Error loading files", style={'color': '#f44336'})
            storage_status = "🔴 Error"
            compression_status = "🔴 Error"
            
    except Exception as e:
        files_list = html.Div(f"Error: {str(e)}", style={'color': '#f44336'})
        storage_status = "🔴 Error"
        compression_status = "🔴 Error"
    
    last_updated = f"Last updated: {datetime.now().strftime('%H:%M:%S')}"
    
    return files_list, api_status, storage_status, compression_status, last_updated

# CSS Styling
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 0;
                background-color: #1a1a1a;
                color: #ffffff;
            }
            .dashboard-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding: 20px;
                background-color: #2a2a2a;
                border-radius: 10px;
            }
            .dashboard-title {
                color: #4CAF50;
                margin-bottom: 10px;
            }
            .section-title {
                color: #4CAF50;
                border-bottom: 2px solid #4CAF50;
                padding-bottom: 10px;
                margin-bottom: 20px;
            }
            .upload-container, .status-container {
                background-color: #2a2a2a;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
            }
            .status-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }
            .status-card {
                background-color: #3a3a3a;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                border: 1px solid #555;
            }
            .status-indicator {
                font-size: 1.2em;
                font-weight: bold;
                margin-top: 10px;
            }
            .last-updated {
                color: #888;
                font-size: 0.9em;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

def main():
    """Main function to run the dashboard"""
    logger.info("Starting QR3 Simple Dashboard...")
    try:
        app.run(debug=False, host='0.0.0.0', port=8050)
    except Exception as e:
        logger.error(f"Error running dashboard: {e}")

if __name__ == '__main__':
    main()
