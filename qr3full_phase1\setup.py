#!/usr/bin/env python3
"""
QR3Full Phase 1 Setup Script
Automated setup for Phase 1 optimizations
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def create_virtual_environment():
    """Create virtual environment"""
    print("🐍 Creating virtual environment...")
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Virtual environment creation failed: {e}")
        return False

def install_requirements():
    """Install requirements"""
    print("📦 Installing requirements...")
    
    # Determine pip path
    if os.name == 'nt':  # Windows
        pip_path = "venv\\Scripts\\pip"
    else:  # Unix/Linux/Mac
        pip_path = "venv/bin/pip"
    
    try:
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        print("✅ Requirements installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Requirements installation failed: {e}")
        return False

def run_phase1_implementation():
    """Run Phase 1 implementation"""
    print("🚀 Running Phase 1 implementation...")
    
    # Determine python path
    if os.name == 'nt':  # Windows
        python_path = "venv\\Scripts\\python"
    else:  # Unix/Linux/Mac
        python_path = "venv/bin/python"
    
    try:
        subprocess.run([python_path, "phase1_optimization_implementation.py"], check=True)
        print("✅ Phase 1 implementation completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Phase 1 implementation failed: {e}")
        return False

def run_tests():
    """Run validation tests"""
    print("🧪 Running validation tests...")
    
    # Determine python path
    if os.name == 'nt':  # Windows
        python_path = "venv\\Scripts\\python"
    else:  # Unix/Linux/Mac
        python_path = "venv/bin/python"
    
    try:
        subprocess.run([python_path, "test_phase1_optimizations.py"], check=True)
        print("✅ Tests passed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Tests failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 QR3FULL PHASE 1 SETUP")
    print("=" * 40)
    
    steps = [
        ("Python Version Check", check_python_version),
        ("Virtual Environment", create_virtual_environment),
        ("Install Requirements", install_requirements),
        ("Phase 1 Implementation", run_phase1_implementation),
        ("Run Tests", run_tests)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            return False
    
    print("\n🎉 SETUP COMPLETE!")
    print("\n📋 Next Steps:")
    print("1. Activate virtual environment:")
    if os.name == 'nt':
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    print("2. Run optimized analysis:")
    print("   python run_optimized_kpi_analysis.py")
    
    print("3. Monitor performance:")
    print("   Check monitoring/phase1/ directory")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
