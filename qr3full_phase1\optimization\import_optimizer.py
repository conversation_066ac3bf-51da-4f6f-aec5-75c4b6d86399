import sys
import time
import importlib
from typing import Dict, Any, Optional
from functools import lru_cache

class ImportOptimizer:
    """Optimize import performance for Phase 1"""

    def __init__(self):
        self.import_cache = {}
        self.import_times = {}
        self.lazy_imports = {}

    @lru_cache(maxsize=128)
    def cached_import(self, module_name: str):
        """Cache imported modules"""
        start_time = time.time()

        try:
            if module_name in sys.modules:
                module = sys.modules[module_name]
            else:
                module = importlib.import_module(module_name)

            import_time = time.time() - start_time
            self.import_times[module_name] = import_time

            return module

        except ImportError as e:
            self.import_times[module_name] = time.time() - start_time
            raise e

    def lazy_import(self, module_name: str):
        """Lazy import that only loads when accessed"""
        class LazyModule:
            def __init__(self, name):
                self._name = name
                self._module = None

            def __getattr__(self, attr):
                if self._module is None:
                    self._module = importlib.import_module(self._name)
                return getattr(self._module, attr)

        if module_name not in self.lazy_imports:
            self.lazy_imports[module_name] = LazyModule(module_name)

        return self.lazy_imports[module_name]

    def preload_common_modules(self):
        """Preload commonly used modules"""
        common_modules = [
            'json', 'os', 'sys', 'time', 'pathlib',
            'typing', 'functools', 'itertools'
        ]

        preloaded = []
        for module_name in common_modules:
            try:
                self.cached_import(module_name)
                preloaded.append(module_name)
            except ImportError:
                pass

        return preloaded

    def get_import_stats(self) -> Dict[str, Any]:
        """Get import performance statistics"""
        total_time = sum(self.import_times.values())

        return {
            "modules_imported": len(self.import_times),
            "total_import_time": total_time,
            "average_import_time": total_time / len(self.import_times) if self.import_times else 0,
            "cached_imports": len(self.import_cache),
            "lazy_imports": len(self.lazy_imports)
        }

# Global import optimizer
_import_optimizer = None

def get_import_optimizer():
    """Get global import optimizer instance"""
    global _import_optimizer
    if _import_optimizer is None:
        _import_optimizer = ImportOptimizer()
    return _import_optimizer

# Optimized import functions
def fast_import(module_name: str):
    """Fast cached import"""
    optimizer = get_import_optimizer()
    return optimizer.cached_import(module_name)

def lazy_import(module_name: str):
    """Lazy import that loads on first use"""
    optimizer = get_import_optimizer()
    return optimizer.lazy_import(module_name)
