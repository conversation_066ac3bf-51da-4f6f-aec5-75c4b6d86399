"""
Unit tests for core data models.

This module tests the ValidationError, ValidationResult, ValidationConfig,
and other core data model classes.
"""

import unittest
from datetime import datetime
from all.data_verification.core.models import (
    ValidationError, ValidationResult, ValidationConfig, ValidationRule,
    ErrorSeverity, ErrorCategory, FileProcessingConfig, ErrorReportingConfig,
    SystemConfig, BatchValidationResult
)


class TestValidationError(unittest.TestCase):
    """Test cases for ValidationError class."""
    
    def test_validation_error_creation(self):
        """Test creating a ValidationError instance."""
        error = ValidationError(
            file_path="test.csv",
            row_number=1,
            column_name="age",
            error_type="range_error",
            error_message="Value out of range",
            actual_value=150,
            expected_format="0-120"
        )
        
        self.assertEqual(error.file_path, "test.csv")
        self.assertEqual(error.row_number, 1)
        self.assertEqual(error.column_name, "age")
        self.assertEqual(error.error_type, "range_error")
        self.assertEqual(error.error_message, "Value out of range")
        self.assertEqual(error.actual_value, 150)
        self.assertEqual(error.expected_format, "0-120")
        self.assertEqual(error.severity, ErrorSeverity.ERROR)
        self.assertEqual(error.category, ErrorCategory.DATA_TYPE)
        self.assertIsInstance(error.timestamp, datetime)
    
    def test_validation_error_to_dict(self):
        """Test converting ValidationError to dictionary."""
        error = ValidationError(
            file_path="test.csv",
            row_number=1,
            column_name="age",
            error_type="range_error",
            error_message="Value out of range",
            actual_value=150,
            expected_format="0-120",
            severity=ErrorSeverity.WARNING,
            category=ErrorCategory.RANGE_VALIDATION
        )
        
        error_dict = error.to_dict()
        
        self.assertEqual(error_dict['file_path'], "test.csv")
        self.assertEqual(error_dict['row_number'], 1)
        self.assertEqual(error_dict['column_name'], "age")
        self.assertEqual(error_dict['severity'], "warning")
        self.assertEqual(error_dict['category'], "range_validation")
        self.assertIn('timestamp', error_dict)
    
    def test_validation_error_str(self):
        """Test string representation of ValidationError."""
        error = ValidationError(
            file_path="test.csv",
            row_number=1,
            column_name="age",
            error_type="range_error",
            error_message="Value out of range",
            actual_value=150,
            expected_format="0-120"
        )
        
        error_str = str(error)
        self.assertIn("ERROR", error_str)
        self.assertIn("test.csv", error_str)
        self.assertIn("1", error_str)
        self.assertIn("age", error_str)
        self.assertIn("Value out of range", error_str)


class TestValidationResult(unittest.TestCase):
    """Test cases for ValidationResult class."""
    
    def test_validation_result_creation(self):
        """Test creating a ValidationResult instance."""
        result = ValidationResult(
            file_path="test.csv",
            is_valid=True,
            processed_rows=100,
            processing_time_ms=250.5,
            memory_usage_mb=12.3
        )
        
        self.assertEqual(result.file_path, "test.csv")
        self.assertTrue(result.is_valid)
        self.assertEqual(result.processed_rows, 100)
        self.assertEqual(result.processing_time_ms, 250.5)
        self.assertEqual(result.memory_usage_mb, 12.3)
        self.assertEqual(result.error_count, 0)
        self.assertEqual(result.warning_count, 0)
        self.assertEqual(result.total_issues, 0)
    
    def test_add_error(self):
        """Test adding errors to ValidationResult."""
        result = ValidationResult(file_path="test.csv", is_valid=True)
        
        error = ValidationError(
            file_path="test.csv",
            row_number=1,
            column_name="age",
            error_type="range_error",
            error_message="Value out of range",
            actual_value=150,
            expected_format="0-120",
            severity=ErrorSeverity.ERROR
        )
        
        result.add_error(error)
        
        self.assertFalse(result.is_valid)
        self.assertEqual(result.error_count, 1)
        self.assertEqual(result.warning_count, 0)
        self.assertEqual(result.total_issues, 1)
    
    def test_add_warning(self):
        """Test adding warnings to ValidationResult."""
        result = ValidationResult(file_path="test.csv", is_valid=True)
        
        warning = ValidationError(
            file_path="test.csv",
            row_number=1,
            column_name="name",
            error_type="format_warning",
            error_message="Unusual format detected",
            actual_value="john doe",
            expected_format="John Doe",
            severity=ErrorSeverity.WARNING
        )
        
        result.add_error(warning)
        
        self.assertTrue(result.is_valid)  # Warnings don't invalidate result
        self.assertEqual(result.error_count, 0)
        self.assertEqual(result.warning_count, 1)
        self.assertEqual(result.total_issues, 1)
    
    def test_get_errors_by_category(self):
        """Test filtering errors by category."""
        result = ValidationResult(file_path="test.csv", is_valid=True)
        
        error1 = ValidationError(
            file_path="test.csv", row_number=1, column_name="age",
            error_type="range_error", error_message="Out of range",
            actual_value=150, expected_format="0-120",
            category=ErrorCategory.RANGE_VALIDATION
        )
        
        error2 = ValidationError(
            file_path="test.csv", row_number=2, column_name="email",
            error_type="format_error", error_message="Invalid format",
            actual_value="invalid", expected_format="<EMAIL>",
            category=ErrorCategory.FORMAT_VALIDATION
        )
        
        result.add_error(error1)
        result.add_error(error2)
        
        range_errors = result.get_errors_by_category(ErrorCategory.RANGE_VALIDATION)
        format_errors = result.get_errors_by_category(ErrorCategory.FORMAT_VALIDATION)
        
        self.assertEqual(len(range_errors), 1)
        self.assertEqual(len(format_errors), 1)
        self.assertEqual(range_errors[0].column_name, "age")
        self.assertEqual(format_errors[0].column_name, "email")
    
    def test_to_dict(self):
        """Test converting ValidationResult to dictionary."""
        result = ValidationResult(
            file_path="test.csv",
            is_valid=False,
            processed_rows=100,
            processing_time_ms=250.5,
            memory_usage_mb=12.3
        )
        
        error = ValidationError(
            file_path="test.csv", row_number=1, column_name="age",
            error_type="range_error", error_message="Out of range",
            actual_value=150, expected_format="0-120"
        )
        result.add_error(error)
        
        result_dict = result.to_dict()
        
        self.assertEqual(result_dict['file_path'], "test.csv")
        self.assertFalse(result_dict['is_valid'])
        self.assertEqual(result_dict['error_count'], 1)
        self.assertEqual(result_dict['processed_rows'], 100)
        self.assertIn('errors', result_dict)
        self.assertEqual(len(result_dict['errors']), 1)


class TestValidationRule(unittest.TestCase):
    """Test cases for ValidationRule class."""
    
    def test_validation_rule_creation(self):
        """Test creating a ValidationRule instance."""
        rule = ValidationRule(
            validator_type="numeric",
            column_pattern="age",
            parameters={"min_value": 0, "max_value": 120},
            priority=10,
            enabled=True
        )
        
        self.assertEqual(rule.validator_type, "numeric")
        self.assertEqual(rule.column_pattern, "age")
        self.assertEqual(rule.parameters["min_value"], 0)
        self.assertEqual(rule.parameters["max_value"], 120)
        self.assertEqual(rule.priority, 10)
        self.assertTrue(rule.enabled)
    
    def test_matches_column_exact(self):
        """Test exact column name matching."""
        rule = ValidationRule(
            validator_type="numeric",
            column_pattern="age"
        )
        
        self.assertTrue(rule.matches_column("age"))
        self.assertFalse(rule.matches_column("name"))
        self.assertFalse(rule.matches_column("age_group"))
    
    def test_matches_column_regex(self):
        """Test regex column name matching."""
        rule = ValidationRule(
            validator_type="numeric",
            column_pattern=".*_id$"
        )
        
        self.assertTrue(rule.matches_column("user_id"))
        self.assertTrue(rule.matches_column("product_id"))
        self.assertFalse(rule.matches_column("id_number"))
        self.assertFalse(rule.matches_column("name"))
    
    def test_format_error_message(self):
        """Test error message formatting."""
        rule = ValidationRule(
            validator_type="numeric",
            column_pattern="age",
            error_message_template="Column {column_name} failed: {error_details}"
        )
        
        message = rule.format_error_message("age", "value out of range")
        self.assertEqual(message, "Column age failed: value out of range")


class TestValidationConfig(unittest.TestCase):
    """Test cases for ValidationConfig class."""
    
    def test_validation_config_creation(self):
        """Test creating a ValidationConfig instance."""
        config = ValidationConfig()
        
        self.assertIsInstance(config.file_patterns, list)
        self.assertIsInstance(config.column_rules, dict)
        self.assertIsInstance(config.global_rules, list)
        self.assertIsInstance(config.file_processing, FileProcessingConfig)
        self.assertIsInstance(config.error_reporting, ErrorReportingConfig)
        self.assertIsInstance(config.system, SystemConfig)
    
    def test_get_rules_for_column(self):
        """Test getting validation rules for a specific column."""
        config = ValidationConfig()
        
        # Add global rule
        global_rule = ValidationRule(
            validator_type="not_empty",
            column_pattern=".*"
        )
        config.global_rules.append(global_rule)
        
        # Add column-specific rule
        age_rule = ValidationRule(
            validator_type="numeric",
            column_pattern="age",
            priority=1
        )
        config.column_rules["age"] = [age_rule]
        
        # Test getting rules for age column
        age_rules = config.get_rules_for_column("age")
        self.assertEqual(len(age_rules), 2)  # global + specific
        self.assertEqual(age_rules[0].validator_type, "numeric")  # Higher priority first
        self.assertEqual(age_rules[1].validator_type, "not_empty")
        
        # Test getting rules for other column
        name_rules = config.get_rules_for_column("name")
        self.assertEqual(len(name_rules), 1)  # Only global rule
        self.assertEqual(name_rules[0].validator_type, "not_empty")
    
    def test_to_dict_and_from_dict(self):
        """Test converting config to dict and back."""
        config = ValidationConfig()
        config.file_patterns = ["*.csv", "*.json"]
        
        rule = ValidationRule(
            validator_type="numeric",
            column_pattern="age",
            parameters={"min_value": 0}
        )
        config.global_rules.append(rule)
        
        # Convert to dict
        config_dict = config.to_dict()
        
        # Convert back from dict
        restored_config = ValidationConfig.from_dict(config_dict)
        
        self.assertEqual(restored_config.file_patterns, ["*.csv", "*.json"])
        self.assertEqual(len(restored_config.global_rules), 1)
        self.assertEqual(restored_config.global_rules[0].validator_type, "numeric")
        self.assertEqual(restored_config.global_rules[0].parameters["min_value"], 0)


class TestBatchValidationResult(unittest.TestCase):
    """Test cases for BatchValidationResult class."""
    
    def test_batch_validation_result_creation(self):
        """Test creating a BatchValidationResult instance."""
        batch_result = BatchValidationResult(
            total_files=5,
            successful_files=0,
            failed_files=0,
            total_errors=0,
            total_warnings=0,
            processing_time_ms=1000.0
        )
        
        self.assertEqual(batch_result.total_files, 5)
        self.assertEqual(batch_result.successful_files, 0)
        self.assertEqual(batch_result.failed_files, 0)
        self.assertEqual(batch_result.success_rate, 0.0)
    
    def test_add_result(self):
        """Test adding validation results to batch result."""
        batch_result = BatchValidationResult(
            total_files=2,
            successful_files=0,
            failed_files=0,
            total_errors=0,
            total_warnings=0,
            processing_time_ms=0.0
        )
        
        # Add successful result
        success_result = ValidationResult(file_path="test1.csv", is_valid=True)
        batch_result.add_result(success_result)
        
        # Add failed result with errors
        fail_result = ValidationResult(file_path="test2.csv", is_valid=False)
        error = ValidationError(
            file_path="test2.csv", row_number=1, column_name="age",
            error_type="error", error_message="Error",
            actual_value="", expected_format=""
        )
        fail_result.add_error(error)
        batch_result.add_result(fail_result)
        
        self.assertEqual(batch_result.successful_files, 1)
        self.assertEqual(batch_result.failed_files, 1)
        self.assertEqual(batch_result.total_errors, 1)
        self.assertEqual(batch_result.success_rate, 50.0)


if __name__ == '__main__':
    unittest.main()