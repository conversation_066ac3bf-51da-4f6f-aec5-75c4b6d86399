{"file_path": "sample.csv", "is_valid": false, "error_count": 2, "warning_count": 1, "processed_rows": 100, "processing_time_ms": 250.5, "memory_usage_mb": 12.3, "timestamp": "2025-07-26T06:42:09.166254", "errors": [{"file_path": "sample.csv", "row_number": 1, "column_name": "age", "error_type": "range_error", "error_message": "Age value 150 is out of valid range (0-120)", "actual_value": "150", "expected_format": "0-120", "severity": "error", "category": "range_validation", "timestamp": "2025-07-26T06:42:09.166254", "context": {}}, {"file_path": "sample.csv", "row_number": 2, "column_name": "email", "error_type": "format_error", "error_message": "Email format is invalid", "actual_value": "invalid-email", "expected_format": "<EMAIL>", "severity": "error", "category": "format_validation", "timestamp": "2025-07-26T06:42:09.166254", "context": {}}], "warnings": [{"file_path": "sample.csv", "row_number": 3, "column_name": "name", "error_type": "format_warning", "error_message": "Name appears to be in lowercase", "actual_value": "john doe", "expected_format": "<PERSON>", "severity": "warning", "category": "format_validation", "timestamp": "2025-07-26T06:42:09.166254", "context": {}}]}