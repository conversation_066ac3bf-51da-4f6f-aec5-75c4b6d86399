# Data Verification System

A comprehensive Python-based tool designed to validate and verify data integrity across various file formats, with initial focus on CSV files. The system provides configurable validation rules, detailed error reporting, and extensible architecture to support multiple data formats and validation criteria.

This system integrates with the QR3D Digital World project to ensure data quality for machine learning pipelines and vector storage operations.

## Features

- **Configurable Validation Rules**: Define validation rules through JSON/YAML configuration files
- **Multiple File Format Support**: Extensible architecture supports CSV, JSON, and other formats
- **Detailed Error Reporting**: Comprehensive error reporting with context and categorization
- **Batch Processing**: Parallel processing of multiple files with performance monitoring
- **Extensible Architecture**: Plugin system for custom validators, readers, and reporters
- **QR3D Integration**: Specialized integration with QR3D Digital World workflows
- **Performance Monitoring**: Built-in memory usage and processing time tracking

## Project Structure

```
all/data_verification/
├── core/                          # Core system components
│   ├── __init__.py               # Core module exports
│   ├── models.py                 # Data models and configuration classes
│   ├── interfaces.py             # Abstract base classes and interfaces
│   └── engine.py                 # Main validation engine
├── validators/                    # Validation components
│   └── __init__.py               # Validator module (to be implemented)
├── readers/                       # File reader components
│   └── __init__.py               # Reader module (to be implemented)
├── reporters/                     # Error reporter components
│   └── __init__.py               # Reporter module (to be implemented)
├── config/                        # Configuration management
│   └── __init__.py               # Config module (to be implemented)
├── tests/                         # Test suite
│   ├── __init__.py               # Test module
│   ├── test_core_models.py       # Tests for data models
│   └── test_core_interfaces.py   # Tests for interfaces and engine
├── demo.py                        # Demonstration script
└── README.md                      # This file
```

## Core Components

### Data Models (`core/models.py`)

- **ValidationError**: Represents a validation error with full context
- **ValidationResult**: Results of validating a single file
- **BatchValidationResult**: Aggregated results from batch operations
- **ValidationConfig**: Main configuration class
- **ValidationRule**: Configuration for individual validation rules
- **ErrorSeverity**: Enumeration for error severity levels
- **ErrorCategory**: Enumeration for error categories

### Interfaces (`core/interfaces.py`)

- **FileReader**: Abstract base class for file readers
- **Validator**: Abstract base class for data validators
- **ErrorReporter**: Abstract base class for error reporters
- **ValidatorFactory**: Factory for creating validator instances
- **FileReaderFactory**: Factory for creating file reader instances
- **ErrorReporterFactory**: Factory for creating error reporter instances

### Validation Engine (`core/engine.py`)

- **ValidationEngine**: Central orchestrator that coordinates validation workflows
- Supports component registration and factory-based creation
- Handles both single-file and batch processing
- Includes performance monitoring and error handling

## Usage Examples

### Basic Usage

```python
from all.data_verification.core.models import ValidationConfig, ValidationRule
from all.data_verification.core.engine import ValidationEngine

# Create configuration
config = ValidationConfig()

# Add validation rules
age_rule = ValidationRule(
    validator_type="numeric_range",
    column_pattern="age",
    parameters={"min_value": 0, "max_value": 120}
)
config.column_rules["age"] = [age_rule]

# Initialize engine
engine = ValidationEngine(config)

# Register components (readers, validators, reporters)
# ... component registration ...

# Validate a file
result = engine.validate_file("data.csv")
print(f"Valid: {result.is_valid}, Errors: {result.error_count}")
```

### Batch Processing

```python
# Validate multiple files
file_paths = ["file1.csv", "file2.csv", "file3.csv"]
batch_result = engine.validate_batch(file_paths)

print(f"Success rate: {batch_result.success_rate:.1f}%")
print(f"Total errors: {batch_result.total_errors}")
```

### Configuration Management

```python
import json

# Save configuration to file
config_dict = config.to_dict()
with open("validation_config.json", "w") as f:
    json.dump(config_dict, f, indent=2)

# Load configuration from file
with open("validation_config.json", "r") as f:
    loaded_config_dict = json.load(f)

restored_config = ValidationConfig.from_dict(loaded_config_dict)
```

## Configuration Format

The system uses a hierarchical configuration structure:

```json
{
  "file_patterns": ["*.csv", "*.json"],
  "global_rules": [
    {
      "validator_type": "not_empty",
      "column_pattern": ".*",
      "priority": 100,
      "enabled": true
    }
  ],
  "column_rules": {
    "age": [
      {
        "validator_type": "numeric_range",
        "column_pattern": "age",
        "parameters": {"min_value": 0, "max_value": 120},
        "priority": 10
      }
    ]
  },
  "file_processing": {
    "batch_size": 1000,
    "parallel_processing": true,
    "memory_limit_mb": 512
  },
  "error_reporting": {
    "console_output": true,
    "file_output": true,
    "max_errors_per_file": 100
  },
  "system": {
    "max_parallel_files": 4,
    "memory_limit_gb": 8,
    "qr3d_integration": true
  }
}
```

## Error Handling

The system provides comprehensive error handling with:

- **Error Severity Levels**: CRITICAL, ERROR, WARNING, INFO
- **Error Categories**: DATA_TYPE, RANGE_VALIDATION, FORMAT_VALIDATION, BUSINESS_RULE, SYSTEM_ERROR, QR3D_COMPATIBILITY
- **Contextual Information**: File path, row number, column name, actual vs expected values
- **Error Aggregation**: Grouping similar errors for easier analysis

## Testing

Run the test suite:

```bash
# Run all tests
python -m pytest all/data_verification/tests/ -v

# Run specific test files
python -m pytest all/data_verification/tests/test_core_models.py -v
python -m pytest all/data_verification/tests/test_core_interfaces.py -v
```

## Demo

Run the demonstration script to see the system in action:

```bash
python all/data_verification/demo.py
```

## Integration with QR3D Digital World

The system is designed to integrate seamlessly with the QR3D Digital World ecosystem:

- **Configuration Integration**: Uses existing QR3D configuration patterns
- **Monitoring Integration**: Hooks into QR3D monitoring infrastructure
- **Performance Optimization**: GPU acceleration support for compatible operations
- **Vector Data Validation**: Specialized validators for vector storage requirements

## Next Steps

The current implementation provides the core foundation. Next steps include:

1. **Implement File Readers**: CSV, JSON, and other format readers
2. **Implement Validators**: Numeric, string, email, and custom validators
3. **Implement Error Reporters**: Console, file, HTML, and API reporters
4. **Configuration Management**: JSON/YAML configuration loading and validation
5. **Batch Processing**: Enhanced parallel processing and progress tracking
6. **QR3D Integration**: Specialized QR3D compatibility validators
7. **Performance Optimization**: GPU acceleration and memory optimization
8. **Documentation**: API documentation and user guides

## Requirements

- Python 3.10+
- psutil (for system monitoring)
- Standard library modules: json, pathlib, datetime, enum, dataclasses, abc, typing, logging, time, traceback, concurrent.futures

## License

This project is part of the QR3D Digital World ecosystem.