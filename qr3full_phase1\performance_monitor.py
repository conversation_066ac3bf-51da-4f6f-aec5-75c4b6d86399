import time
import psutil
import json
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class PerformanceMetric:
    timestamp: float
    operation: str
    execution_time: float
    memory_usage_mb: float
    cpu_percent: float
    cache_hit_rate: float = 0.0
    
class PerformanceMonitor:
    """Monitor performance improvements from Phase 1 optimizations"""
    
    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.baseline_metrics = {}
        self.monitoring_enabled = True
        
        # Create monitoring directory
        self.monitor_dir = Path("monitoring/phase1")
        self.monitor_dir.mkdir(parents=True, exist_ok=True)
    
    def start_operation(self, operation_name: str) -> Dict[str, Any]:
        """Start monitoring an operation"""
        if not self.monitoring_enabled:
            return {}
        
        return {
            "operation": operation_name,
            "start_time": time.time(),
            "start_memory": self.get_memory_usage(),
            "start_cpu": psutil.cpu_percent()
        }
    
    def end_operation(self, operation_context: Dict[str, Any], 
                     cache_hit_rate: float = 0.0) -> PerformanceMetric:
        """End monitoring an operation and record metrics"""
        if not self.monitoring_enabled or not operation_context:
            return None
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        end_cpu = psutil.cpu_percent()
        
        metric = PerformanceMetric(
            timestamp=end_time,
            operation=operation_context["operation"],
            execution_time=end_time - operation_context["start_time"],
            memory_usage_mb=end_memory,
            cpu_percent=end_cpu,
            cache_hit_rate=cache_hit_rate
        )
        
        self.metrics.append(metric)
        return metric
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def set_baseline(self, operation: str, metrics: Dict[str, float]):
        """Set baseline metrics for comparison"""
        self.baseline_metrics[operation] = metrics
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary with improvements"""
        if not self.metrics:
            return {"status": "no_metrics"}
        
        # Group metrics by operation
        operation_metrics = {}
        for metric in self.metrics:
            if metric.operation not in operation_metrics:
                operation_metrics[metric.operation] = []
            operation_metrics[metric.operation].append(metric)
        
        summary = {}
        for operation, metrics_list in operation_metrics.items():
            avg_time = sum(m.execution_time for m in metrics_list) / len(metrics_list)
            avg_memory = sum(m.memory_usage_mb for m in metrics_list) / len(metrics_list)
            avg_cache_hit = sum(m.cache_hit_rate for m in metrics_list) / len(metrics_list)
            
            summary[operation] = {
                "average_execution_time": avg_time,
                "average_memory_usage_mb": avg_memory,
                "average_cache_hit_rate": avg_cache_hit,
                "sample_count": len(metrics_list)
            }
            
            # Compare with baseline if available
            if operation in self.baseline_metrics:
                baseline = self.baseline_metrics[operation]
                
                if "execution_time" in baseline:
                    time_improvement = (baseline["execution_time"] - avg_time) / baseline["execution_time"]
                    summary[operation]["time_improvement_percent"] = time_improvement * 100
                
                if "memory_usage" in baseline:
                    memory_improvement = (baseline["memory_usage"] - avg_memory) / baseline["memory_usage"]
                    summary[operation]["memory_improvement_percent"] = memory_improvement * 100
        
        return summary

# Global performance monitor
_performance_monitor = None

def get_performance_monitor():
    """Get global performance monitor instance"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

def monitor_operation(operation_name: str):
    """Decorator to monitor operation performance"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()
            context = monitor.start_operation(operation_name)
            
            try:
                result = func(*args, **kwargs)
                
                # Extract cache hit rate if available
                cache_hit_rate = 0.0
                if isinstance(result, dict) and '_cache_info' in result:
                    cache_hit_rate = 0.0 if result['_cache_info'].get('cache_miss', True) else 1.0
                
                monitor.end_operation(context, cache_hit_rate)
                return result
                
            except Exception as e:
                monitor.end_operation(context)
                raise e
        
        return wrapper
    return decorator
