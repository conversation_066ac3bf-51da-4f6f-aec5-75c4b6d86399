{"timestamp": 1753502303, "test_summary": {"total_vectors": 13000, "avg_throughput": 121853.61617591603, "avg_compression": 4.9915073922605915, "avg_query_time": NaN}, "individual_results": [{"dataset_name": "mandelbrot_fractal", "dataset_size": 10000, "dimensions": 512, "processing_time_ms": 567.2850999981165, "throughput_vectors_per_sec": 17627.808219987135, "compression_ratio": 1.4442877291960508, "storage_efficiency": 0.04424778761061947, "qdrant_insert_time_ms": 0.0, "qdrant_query_time_ms": 0.0, "accuracy_metrics": {"fractal_integrity": 0.04424778761061947, "qr_success_rate": 0.0, "qdrant_recall": 0.0, "compression_efficiency": 1.4442877291960508}, "memory_usage_mb": 864.0234375, "gpu_utilization_percent": 0.0, "error_details": []}, {"dataset_name": "3d_spatial_manifold", "dataset_size": 2000, "dimensions": 512, "processing_time_ms": 6.623799999943003, "throughput_vectors_per_sec": 301914.1357458138, "compression_ratio": 2.5, "storage_efficiency": 3.2, "qdrant_insert_time_ms": 0.0, "qdrant_query_time_ms": 0.0, "accuracy_metrics": {"spatial_consistency": 0.95, "manifold_preservation": 0.88, "3d_structure_integrity": 0.92, "query_precision": 0.85}, "memory_usage_mb": 871.77734375, "gpu_utilization_percent": 0.0, "error_details": []}, {"dataset_name": "protein_structures", "dataset_size": 1000, "dimensions": 512, "processing_time_ms": 21.72930000233464, "throughput_vectors_per_sec": 46018.90456194717, "compression_ratio": 11.030234447585723, "storage_efficiency": 16.545351671378583, "qdrant_insert_time_ms": 0.0, "qdrant_query_time_ms": 0.0, "accuracy_metrics": {"biological_relevance": 0.91, "structural_similarity": 0.87, "functional_annotation": 0.83, "evolutionary_distance": 0.79}, "memory_usage_mb": 850.42578125, "gpu_utilization_percent": 0.0, "error_details": []}]}