"""
Core components for the Data Verification System.

This module contains the fundamental interfaces, data models, and engine
that form the foundation of the data verification system.
"""

from .interfaces import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON>orter
from .models import ValidationError, ValidationResult, ValidationConfig
from .engine import ValidationEngine

__all__ = [
    'FileReader',
    'Validator', 
    'ErrorReporter',
    'ValidationError',
    'ValidationResult',
    'ValidationConfig',
    'ValidationEngine'
]