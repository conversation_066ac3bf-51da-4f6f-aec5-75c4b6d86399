{"file_patterns": ["*.csv", "*.json"], "column_rules": {"age": [{"validator_type": "numeric_range", "column_pattern": "age", "parameters": {"min_value": 0, "max_value": 120}, "priority": 10, "enabled": true, "error_message_template": "Age in column {column_name} must be between 0 and 120"}], "email": [{"validator_type": "email_format", "column_pattern": "email", "parameters": {}, "priority": 20, "enabled": true, "error_message_template": "Email in column {column_name} has invalid format"}]}, "global_rules": [{"validator_type": "not_empty", "column_pattern": ".*", "parameters": {}, "priority": 100, "enabled": true, "error_message_template": "Column {column_name} cannot be empty"}], "file_processing": {"batch_size": 1000, "parallel_processing": true, "streaming_mode": false, "memory_limit_mb": 512, "timeout_seconds": 300, "retry_attempts": 3, "skip_header_rows": 1, "encoding": "utf-8"}, "error_reporting": {"console_output": true, "file_output": true, "output_directory": "validation_reports", "report_format": "json", "include_context": true, "group_similar_errors": true, "max_errors_per_file": 100, "include_warnings": true}, "system": {"max_parallel_files": 4, "memory_limit_gb": 8, "streaming_threshold_mb": 100, "enable_gpu_acceleration": false, "monitoring_integration": true, "qr3d_integration": true, "log_level": "INFO", "temp_directory": "temp"}}