#!/usr/bin/env python3
"""
Test Phase 1 Optimizations
Validates that Phase 1 optimizations are working correctly
"""

import sys
import time
import json
from pathlib import Path

# Add optimization directory to path
sys.path.insert(0, str(Path(__file__).parent / "optimization"))

def test_caching_system():
    """Test the basic caching system"""
    print("🧪 Testing Caching System...")

    try:
        from basic_cache_manager import get_cache_manager
        from cached_analyzer import CachedAnalyzer

        # Test cache manager
        cache = get_cache_manager()

        # Test cache operations
        test_key = "test_file_123"
        test_data = {"analysis": "test", "score": 95}

        # Cache data
        cache.cache_result(test_key, test_data)

        # Retrieve data
        retrieved = cache.get_cached_result(test_key)

        assert retrieved == test_data, "Cache retrieval failed"

        # Check stats
        stats = cache.get_cache_stats()
        assert stats["hit_count"] > 0, "Cache hit count not updated"

        print("   ✅ Cache manager working correctly")
        return True

    except Exception as e:
        print(f"   ❌ Cache test failed: {e}")
        return False

def test_memory_optimization():
    """Test memory optimization features"""
    print("🧪 Testing Memory Optimization...")

    try:
        from memory_optimizer import get_memory_optimizer, memory_efficient

        optimizer = get_memory_optimizer()

        # Test memory monitoring
        initial_memory = optimizer.get_memory_usage()
        assert initial_memory > 0, "Memory monitoring not working"

        # Test memory efficient decorator
        @memory_efficient
        def test_function():
            # Simulate some work
            data = [i for i in range(1000)]
            return {"processed": len(data)}

        result = test_function()
        assert "processed" in result, "Memory efficient function failed"
        assert "_memory_info" in result, "Memory info not added"

        print("   ✅ Memory optimization working correctly")
        return True

    except Exception as e:
        print(f"   ❌ Memory optimization test failed: {e}")
        return False

def test_import_optimization():
    """Test import optimization features"""
    print("🧪 Testing Import Optimization...")

    try:
        from import_optimizer import get_import_optimizer, fast_import, lazy_import

        optimizer = get_import_optimizer()

        # Test cached import
        json_module = fast_import('json')
        assert hasattr(json_module, 'loads'), "Fast import failed"

        # Test lazy import
        os_module = lazy_import('os')
        assert hasattr(os_module, 'path'), "Lazy import failed"

        # Test preloading
        preloaded = optimizer.preload_common_modules()
        assert len(preloaded) > 0, "Module preloading failed"

        print("   ✅ Import optimization working correctly")
        return True

    except Exception as e:
        print(f"   ❌ Import optimization test failed: {e}")
        return False

def main():
    """Main test execution"""
    print("🧪 PHASE 1 OPTIMIZATION VALIDATION")
    print("=" * 50)

    tests = [
        ("Caching System", test_caching_system),
        ("Memory Optimization", test_memory_optimization),
        ("Import Optimization", test_import_optimization)
    ]

    results = {}
    passed = 0

    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        try:
            success = test_func()
            results[test_name] = success
            if success:
                passed += 1
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results[test_name] = False

    print(f"\n📊 TEST RESULTS")
    print("=" * 30)
    print(f"✅ Passed: {passed}/{len(tests)}")
    print(f"❌ Failed: {len(tests) - passed}/{len(tests)}")

    if passed == len(tests):
        print("\n🎉 ALL PHASE 1 OPTIMIZATIONS VALIDATED!")
        print("🚀 System ready for performance testing")

    else:
        print("\n⚠️  Some tests failed. Check implementation.")

    # Save test results
    with open("phase1_test_results.json", "w") as f:
        json.dump({
            "timestamp": time.time(),
            "tests_passed": passed,
            "tests_total": len(tests),
            "success_rate": passed / len(tests),
            "detailed_results": results
        }, f, indent=2)

    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
