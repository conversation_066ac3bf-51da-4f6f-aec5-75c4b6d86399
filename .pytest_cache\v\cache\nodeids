["all/data_verification/tests/test_core_interfaces.py::TestCoreInterfaces::test_error_reporter_interface", "all/data_verification/tests/test_core_interfaces.py::TestCoreInterfaces::test_file_reader_interface", "all/data_verification/tests/test_core_interfaces.py::TestCoreInterfaces::test_validator_interface", "all/data_verification/tests/test_core_interfaces.py::TestValidationEngine::test_component_registration", "all/data_verification/tests/test_core_interfaces.py::TestValidationEngine::test_engine_initialization", "all/data_verification/tests/test_core_interfaces.py::TestValidationEngine::test_get_system_info", "all/data_verification/tests/test_core_interfaces.py::TestValidationEngine::test_validate_batch", "all/data_verification/tests/test_core_interfaces.py::TestValidationEngine::test_validate_file_success", "all/data_verification/tests/test_core_interfaces.py::TestValidationEngine::test_validate_file_with_errors", "all/data_verification/tests/test_core_models.py::TestBatchValidationResult::test_add_result", "all/data_verification/tests/test_core_models.py::TestBatchValidationResult::test_batch_validation_result_creation", "all/data_verification/tests/test_core_models.py::TestValidationConfig::test_get_rules_for_column", "all/data_verification/tests/test_core_models.py::TestValidationConfig::test_to_dict_and_from_dict", "all/data_verification/tests/test_core_models.py::TestValidationConfig::test_validation_config_creation", "all/data_verification/tests/test_core_models.py::TestValidationError::test_validation_error_creation", "all/data_verification/tests/test_core_models.py::TestValidationError::test_validation_error_str", "all/data_verification/tests/test_core_models.py::TestValidationError::test_validation_error_to_dict", "all/data_verification/tests/test_core_models.py::TestValidationResult::test_add_error", "all/data_verification/tests/test_core_models.py::TestValidationResult::test_add_warning", "all/data_verification/tests/test_core_models.py::TestValidationResult::test_get_errors_by_category", "all/data_verification/tests/test_core_models.py::TestValidationResult::test_to_dict", "all/data_verification/tests/test_core_models.py::TestValidationResult::test_validation_result_creation", "all/data_verification/tests/test_core_models.py::TestValidationRule::test_format_error_message", "all/data_verification/tests/test_core_models.py::TestValidationRule::test_matches_column_exact", "all/data_verification/tests/test_core_models.py::TestValidationRule::test_matches_column_regex", "all/data_verification/tests/test_core_models.py::TestValidationRule::test_validation_rule_creation", "all/test_newton_expert.py::TestForceBasedOptimizationFramework::test_add_mechanical_system", "all/test_newton_expert.py::TestForceBasedOptimizationFramework::test_force_calculation_between_masses", "all/test_newton_expert.py::TestForceBasedOptimizationFramework::test_force_calculation_precision_validation", "all/test_newton_expert.py::TestForceBasedOptimizationFramework::test_framework_initialization", "all/test_newton_expert.py::TestForceBasedOptimizationFramework::test_optimization_convergence", "all/test_newton_expert.py::TestForceBasedOptimizationFramework::test_system_forces_calculation", "all/test_newton_expert.py::TestIntegrationWithFractalQR::test_persona_face_data_update", "all/test_newton_expert.py::TestIntegrationWithFractalQR::test_persona_face_initialization", "all/test_newton_expert.py::TestNewtonExpert::test_compression_metrics", "all/test_newton_expert.py::TestNewtonExpert::test_error_handling", "all/test_newton_expert.py::TestNewtonExpert::test_expert_initialization", "all/test_newton_expert.py::TestNewtonExpert::test_mathematical_foundation", "all/test_newton_expert.py::TestNewtonExpert::test_optimization_query", "all/test_newton_expert.py::TestNewtonExpert::test_performance_metrics_tracking", "all/test_newton_expert.py::TestNewtonExpert::test_performance_validation", "all/test_newton_expert.py::TestNewtonExpert::test_query_processing_basic", "all/test_newton_expert.py::TestNewtonExpert::test_query_processing_with_context", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_analyze_impact", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_analyze_safety_comprehensive", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_analyze_test_compatibility", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_configuration_integration", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_create_rollback_plan", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_effort_estimation", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_eliminate_duplicate_incremental_steps", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_error_handling", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_extract_method_incremental_steps", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_functionality_preservation_validation", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_functionality_preservation_with_invalid_syntax", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_generate_incremental_steps", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_incremental_step_dependencies", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_public_interface_detection", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_qr3d_specific_validation_commands", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_rename_variable_incremental_steps", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_risk_assessment", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_safety_checkpoints_generation", "codedoc/test_refactoring_safety_analyzer.py::TestRefactoringSafetyAnalyzer::test_safety_level_thresholds"]