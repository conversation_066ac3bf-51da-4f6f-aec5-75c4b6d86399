"""
MoE Routing System Implementation - Fixed Version
Parallel Postulate Transformer MoE - Task 10

This module implements the Mixture of Experts routing system with domain-aware expert selection,
query-expert matching optimization, multi-expert response integration, and performance monitoring.
"""

import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
import re
from collections import defaultdict
import random

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Query:
    """Represents a query for expert processing"""
    content: str
    domain: Optional[str] = None
    complexity_level: int = 1
    required_experts: List[str] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ExpertResponse:
    """Response from a persona expert"""
    expert_name: str
    response_content: Dict[str, Any]
    confidence_score: float
    mathematical_reasoning: str
    processing_time: float
    success: bool = True
    error_message: Optional[str] = None
    
    def validate_response(self) -> bool:
        """Validate response quality and accuracy"""
        if not self.success:
            return False
        
        required_fields = ['expert_name', 'domain', 'mathematical_expression']
        for field in required_fields:
            if field not in self.response_content:
                return False
        
        return self.confidence_score >= 0.5

@dataclass
class CollaborativeResponse:
    """Response from multi-expert collaboration"""
    primary_expert: str
    contributing_experts: List[str]
    combined_response: Dict[str, Any]
    collaboration_score: float
    emergent_properties: List[str] = field(default_factory=list)
    processing_time: float = 0.0
    
    def measure_emergence(self) -> float:
        """Measure emergent intelligence properties"""
        base_emergence = self.collaboration_score
        expert_diversity_bonus = len(self.contributing_experts) * 0.1
        emergence_bonus = len(self.emergent_properties) * 0.05
        total_emergence = min(1.0, base_emergence + expert_diversity_bonus + emergence_bonus)
        return total_emergence


class DomainClassifier:
    """Problem type analysis and routing decisions"""
    
    def __init__(self):
        self.domain_patterns = {
            'logical_reasoning': [
                r'\b(logic|logical|reasoning|premise|conclusion|axiom|proof|theorem)\b',
                r'\b(if.*then|therefore|because|since|given that)\b',
                r'\b(true|false|valid|invalid|consistent|inconsistent)\b',
                r'\b(∀|∃|∧|∨|¬|→|↔)\b'
            ],
            'motion_dynamics': [
                r'\b(motion|movement|velocity|acceleration|trajectory|kinematic)\b',
                r'\b(position|displacement|speed|force|momentum)\b',
                r'\b(oscillation|vibration|wave|frequency|amplitude)\b',
                r'\b(d².*dt²|derivative|differential|equation)\b'
            ],
            'relativistic_processing': [
                r'\b(relativity|relativistic|spacetime|curvature|tensor)\b',
                r'\b(information.*density|multi.*dimensional|spacetime)\b',
                r'\b(Einstein|general.*relativity|special.*relativity)\b',
                r'\b(R_μν|metric.*tensor|Ricci|curvature)\b'
            ],
            'force_optimization': [
                r'\b(force|forces|optimization|mechanical|system)\b',
                r'\b(Newton|gravitational|mass|weight|pressure)\b',
                r'\b(optimize|minimize|maximize|convergence|equilibrium)\b',
                r'\b(F_ij|G_info|mechanical.*system)\b'
            ],
            'computational_architecture': [
                r'\b(computation|algorithm|complexity|efficiency|Turing)\b',
                r'\b(state.*machine|automaton|computational|architecture)\b',
                r'\b(O\(.*\)|time.*complexity|space.*complexity)\b',
                r'\b(δ\(.*\)|transition|state|symbol)\b'
            ],
            'experimental_validation': [
                r'\b(experiment|experimental|validation|measurement|test)\b',
                r'\b(reproducibility|reliability|stability|decay|analysis)\b',
                r'\b(Curie|radioactive|exponential.*decay|half.*life)\b',
                r'\b(N_i\(t\)|λ_i|decay.*constant)\b'
            ]
        }
        
        self.domain_weights = {
            'logical_reasoning': 1.0,
            'motion_dynamics': 0.9,
            'relativistic_processing': 0.8,
            'force_optimization': 0.9,
            'computational_architecture': 0.85,
            'experimental_validation': 0.95
        }
        
        self.classification_metrics = {
            'queries_classified': 0,
            'classification_accuracy': 0.0,
            'average_classification_time': 0.0,
            'domain_distribution': defaultdict(int)
        }
        
        logger.info("DomainClassifier initialized with 6 domain patterns")
    
    def classify_domain(self, query: Query) -> str:
        """Classify query domain based on content analysis"""
        start_time = time.time()
        
        if query.domain and query.domain in self.domain_patterns:
            classification_time = time.time() - start_time
            self._update_classification_metrics(query.domain, classification_time)
            return query.domain
        
        domain_scores = {}
        query_text = query.content.lower()
        
        for domain, patterns in self.domain_patterns.items():
            score = 0.0
            for pattern in patterns:
                matches = re.findall(pattern, query_text, re.IGNORECASE)
                score += len(matches) * self.domain_weights[domain]
            domain_scores[domain] = score / len(patterns)
        
        if domain_scores:
            primary_domain = max(domain_scores, key=domain_scores.get)
            if domain_scores[primary_domain] < 0.1:
                primary_domain = 'general'
        else:
            primary_domain = 'general'
        
        classification_time = time.time() - start_time
        self._update_classification_metrics(primary_domain, classification_time)
        
        return primary_domain
    
    def get_domain_probabilities(self, query: Query) -> Dict[str, float]:
        """Get probability distribution across all domains"""
        query_text = query.content.lower()
        domain_scores = {}
        
        for domain, patterns in self.domain_patterns.items():
            score = 0.0
            for pattern in patterns:
                matches = re.findall(pattern, query_text, re.IGNORECASE)
                score += len(matches) * self.domain_weights[domain]
            domain_scores[domain] = score / len(patterns)
        
        total_score = sum(domain_scores.values())
        if total_score > 0:
            probabilities = {domain: score / total_score for domain, score in domain_scores.items()}
        else:
            num_domains = len(self.domain_patterns)
            probabilities = {domain: 1.0 / num_domains for domain in self.domain_patterns}
        
        return probabilities
    
    def suggest_multi_domain_routing(self, query: Query, threshold: float = 0.3) -> List[str]:
        """Suggest multiple domains for complex queries"""
        probabilities = self.get_domain_probabilities(query)
        suggested_domains = [
            domain for domain, prob in probabilities.items()
            if prob >= threshold
        ]
        suggested_domains.sort(key=lambda d: probabilities[d], reverse=True)
        return suggested_domains
    
    def _update_classification_metrics(self, domain: str, classification_time: float):
        """Update classification performance metrics"""
        self.classification_metrics['queries_classified'] += 1
        self.classification_metrics['domain_distribution'][domain] += 1
        
        current_avg = self.classification_metrics['average_classification_time']
        total_queries = self.classification_metrics['queries_classified']
        
        new_avg = ((current_avg * (total_queries - 1)) + classification_time) / total_queries
        self.classification_metrics['average_classification_time'] = new_avg
    
    def get_classification_metrics(self) -> Dict[str, Any]:
        """Get classification performance metrics"""
        return {
            'queries_classified': self.classification_metrics['queries_classified'],
            'average_classification_time': self.classification_metrics['average_classification_time'],
            'domain_distribution': dict(self.classification_metrics['domain_distribution']),
            'classification_accuracy': self.classification_metrics['classification_accuracy']
        }


class ExpertSelector:
    """Expert selection with optimization for query-expert matching"""
    
    def __init__(self, persona_experts: Dict[str, Any]):
        self.persona_experts = persona_experts
        
        self.expert_domains = {
            'aristotle': 'logical_reasoning',
            'galileo': 'motion_dynamics',
            'einstein': 'relativistic_processing',
            'newton': 'force_optimization',
            'turing': 'computational_architecture',
            'curie': 'experimental_validation'
        }
        
        self.domain_experts = {v: k for k, v in self.expert_domains.items()}
        
        self.expert_performance = {
            expert_name: {
                'success_rate': 1.0,
                'average_response_time': 0.0,
                'confidence_score': 1.0,
                'load_factor': 0.0,
                'queries_processed': 0
            }
            for expert_name in self.persona_experts.keys()
        }
        
        self.selection_weights = {
            'domain_match': 0.4,
            'performance_score': 0.3,
            'load_balance': 0.2,
            'confidence_history': 0.1
        }
        
        logger.info(f"ExpertSelector initialized with {len(self.persona_experts)} experts")
    
    def select_expert(self, domain: str, query: Query) -> str:
        """Select the most appropriate expert for a query"""
        if domain in self.domain_experts:
            primary_expert = self.domain_experts[domain]
        else:
            primary_expert = self._select_best_available_expert(query)
        
        if self._is_expert_available(primary_expert):
            return primary_expert
        
        alternative_expert = self._find_alternative_expert(domain, primary_expert)
        return alternative_expert
    
    def select_multiple_experts(self, query: Query, max_experts: int = 3) -> List[str]:
        """Select multiple experts for collaborative processing"""
        classifier = DomainClassifier()
        domain_probabilities = classifier.get_domain_probabilities(query)
        
        expert_scores = {}
        
        for expert_name in self.persona_experts.keys():
            expert_domain = self.expert_domains[expert_name]
            
            domain_relevance = domain_probabilities.get(expert_domain, 0.0)
            performance_score = self._calculate_expert_performance_score(expert_name)
            load_factor = 1.0 - self.expert_performance[expert_name]['load_factor']
            
            total_score = (
                domain_relevance * self.selection_weights['domain_match'] +
                performance_score * self.selection_weights['performance_score'] +
                load_factor * self.selection_weights['load_balance']
            )
            
            expert_scores[expert_name] = total_score
        
        sorted_experts = sorted(expert_scores.items(), key=lambda x: x[1], reverse=True)
        selected_experts = [expert for expert, score in sorted_experts[:max_experts]]
        
        return selected_experts
    
    def _select_best_available_expert(self, query: Query) -> str:
        """Select best available expert when domain is unclear"""
        expert_scores = {}
        
        for expert_name in self.persona_experts.keys():
            performance_score = self._calculate_expert_performance_score(expert_name)
            load_factor = 1.0 - self.expert_performance[expert_name]['load_factor']
            
            total_score = performance_score * 0.6 + load_factor * 0.4
            expert_scores[expert_name] = total_score
        
        return max(expert_scores, key=expert_scores.get)
    
    def _find_alternative_expert(self, domain: str, unavailable_expert: str) -> str:
        """Find alternative expert when primary is unavailable"""
        available_experts = [
            expert for expert in self.persona_experts.keys()
            if expert != unavailable_expert and self._is_expert_available(expert)
        ]
        
        if not available_experts:
            return min(
                self.persona_experts.keys(),
                key=lambda e: self.expert_performance[e]['load_factor']
            )
        
        best_expert = max(
            available_experts,
            key=lambda e: self._calculate_expert_performance_score(e)
        )
        
        return best_expert
    
    def _is_expert_available(self, expert_name: str) -> bool:
        """Check if expert is available for processing"""
        performance = self.expert_performance[expert_name]
        
        if performance['load_factor'] > 0.8:
            return False
        
        if performance['success_rate'] < 0.5:
            return False
        
        return True
    
    def _calculate_expert_performance_score(self, expert_name: str) -> float:
        """Calculate overall performance score for expert"""
        performance = self.expert_performance[expert_name]
        
        score = (
            performance['success_rate'] * 0.4 +
            (1.0 - min(performance['average_response_time'] / 10.0, 1.0)) * 0.3 +
            performance['confidence_score'] * 0.3
        )
        
        return score
    
    def update_expert_performance(self, expert_name: str, response: ExpertResponse):
        """Update expert performance metrics based on response"""
        if expert_name not in self.expert_performance:
            return
        
        performance = self.expert_performance[expert_name]
        queries_processed = performance['queries_processed']
        
        current_success_rate = performance['success_rate']
        new_success = 1.0 if response.success else 0.0
        performance['success_rate'] = (
            (current_success_rate * queries_processed + new_success) / (queries_processed + 1)
        )
        
        current_avg_time = performance['average_response_time']
        performance['average_response_time'] = (
            (current_avg_time * queries_processed + response.processing_time) / (queries_processed + 1)
        )
        
        current_confidence = performance['confidence_score']
        performance['confidence_score'] = (
            (current_confidence * queries_processed + response.confidence_score) / (queries_processed + 1)
        )
        
        performance['queries_processed'] += 1
    
    def get_expert_load_status(self) -> Dict[str, Dict[str, float]]:
        """Get current load status for all experts"""
        return {
            expert_name: {
                'load_factor': performance['load_factor'],
                'success_rate': performance['success_rate'],
                'average_response_time': performance['average_response_time'],
                'queries_processed': performance['queries_processed']
            }
            for expert_name, performance in self.expert_performance.items()
        }
    
    def optimize_selection_weights(self, performance_feedback: Dict[str, float]):
        """Optimize selection weights based on performance feedback"""
        if performance_feedback.get('accuracy_improvement', 0) > 0.1:
            self.selection_weights['domain_match'] += 0.05
            self.selection_weights['performance_score'] -= 0.025
            self.selection_weights['load_balance'] -= 0.025
        
        total_weight = sum(self.selection_weights.values())
        for key in self.selection_weights:
            self.selection_weights[key] /= total_weight


class ResponseCombiner:
    """Multi-expert response integration"""
    
    def __init__(self):
        self.combination_strategies = {
            'weighted_average': self._weighted_average_combination,
            'consensus_building': self._consensus_building_combination,
            'emergent_synthesis': self._emergent_synthesis_combination
        }
        
        self.combination_metrics = {
            'responses_combined': 0,
            'average_combination_time': 0.0,
            'collaboration_effectiveness': 0.0,
            'emergent_properties_detected': 0
        }
    
    def combine_responses(self, responses: List[ExpertResponse], 
                         strategy: str = 'emergent_synthesis') -> CollaborativeResponse:
        """Combine multiple expert responses into collaborative response"""
        start_time = time.time()
        
        if not responses:
            return CollaborativeResponse(
                primary_expert='none',
                contributing_experts=[],
                combined_response={'error': 'No responses to combine'},
                collaboration_score=0.0
            )
        
        successful_responses = [r for r in responses if r.success and r.validate_response()]
        
        if not successful_responses:
            return CollaborativeResponse(
                primary_expert=responses[0].expert_name,
                contributing_experts=[r.expert_name for r in responses],
                combined_response={'error': 'No successful responses to combine'},
                collaboration_score=0.0
            )
        
        combination_func = self.combination_strategies.get(strategy, self._emergent_synthesis_combination)
        combined_response = combination_func(successful_responses)
        
        processing_time = time.time() - start_time
        combined_response.processing_time = processing_time
        
        self._update_combination_metrics(combined_response, processing_time)
        
        return combined_response
    
    def _weighted_average_combination(self, responses: List[ExpertResponse]) -> CollaborativeResponse:
        """Combine responses using weighted average based on confidence scores"""
        total_confidence = sum(r.confidence_score for r in responses)
        weights = [r.confidence_score / total_confidence for r in responses] if total_confidence > 0 else [1.0 / len(responses)] * len(responses)
        
        primary_expert = max(responses, key=lambda r: r.confidence_score).expert_name
        
        combined_content = {
            'combination_strategy': 'weighted_average',
            'expert_responses': {},
            'confidence_distribution': {}
        }
        
        for i, response in enumerate(responses):
            expert_name = response.expert_name
            weight = weights[i]
            
            combined_content['expert_responses'][expert_name] = response.response_content
            combined_content['confidence_distribution'][expert_name] = {
                'confidence': response.confidence_score,
                'weight': weight,
                'processing_time': response.processing_time
            }
        
        collaboration_score = np.mean([r.confidence_score for r in responses])
        
        return CollaborativeResponse(
            primary_expert=primary_expert,
            contributing_experts=[r.expert_name for r in responses],
            combined_response=combined_content,
            collaboration_score=collaboration_score,
            emergent_properties=['weighted_consensus']
        )
    
    def _consensus_building_combination(self, responses: List[ExpertResponse]) -> CollaborativeResponse:
        """Build consensus from expert responses"""
        consensus_areas = []
        disagreement_areas = []
        
        expert_domains = {}
        for response in responses:
            expert_name = response.expert_name
            content = response.response_content
            expert_domains[expert_name] = content.get('domain', 'unknown')
        
        domain_consensus = len(set(expert_domains.values())) == 1
        if domain_consensus:
            consensus_areas.append('domain_agreement')
        else:
            disagreement_areas.append('domain_disagreement')
        
        primary_expert = responses[0].expert_name
        
        combined_content = {
            'combination_strategy': 'consensus_building',
            'consensus_areas': consensus_areas,
            'disagreement_areas': disagreement_areas,
            'expert_contributions': {r.expert_name: r.response_content for r in responses},
            'consensus_strength': len(consensus_areas) / (len(consensus_areas) + len(disagreement_areas)) if (consensus_areas or disagreement_areas) else 0.5
        }
        
        collaboration_score = combined_content['consensus_strength']
        
        emergent_properties = ['consensus_formation']
        if len(responses) > 2:
            emergent_properties.append('multi_expert_agreement')
        
        return CollaborativeResponse(
            primary_expert=primary_expert,
            contributing_experts=[r.expert_name for r in responses],
            combined_response=combined_content,
            collaboration_score=collaboration_score,
            emergent_properties=emergent_properties
        )
    
    def _emergent_synthesis_combination(self, responses: List[ExpertResponse]) -> CollaborativeResponse:
        """Synthesize responses to identify emergent properties"""
        sorted_responses = sorted(responses, key=lambda r: r.confidence_score, reverse=True)
        primary_expert = sorted_responses[0].expert_name
        
        emergent_properties = []
        
        domains = set(r.response_content.get('domain', 'unknown') for r in responses)
        if len(domains) > 1:
            emergent_properties.append('cross_domain_synthesis')
        
        expressions = [r.response_content.get('mathematical_expression', '') for r in responses]
        if len([e for e in expressions if e]) > 1:
            emergent_properties.append('mathematical_integration')
        
        confidence_scores = [r.confidence_score for r in responses]
        if max(confidence_scores) - min(confidence_scores) < 0.2:
            emergent_properties.append('confidence_convergence')
        
        combined_content = {
            'combination_strategy': 'emergent_synthesis',
            'emergent_properties': emergent_properties,
            'expert_contributions': {r.expert_name: r.response_content for r in responses},
            'synthesis_analysis': {
                'domain_diversity': len(domains),
                'mathematical_complexity': len([e for e in expressions if e]),
                'confidence_alignment': 1.0 - (max(confidence_scores) - min(confidence_scores))
            }
        }
        
        base_score = np.mean(confidence_scores)
        emergence_bonus = len(emergent_properties) * 0.1
        collaboration_score = min(1.0, base_score + emergence_bonus)
        
        return CollaborativeResponse(
            primary_expert=primary_expert,
            contributing_experts=[r.expert_name for r in responses],
            combined_response=combined_content,
            collaboration_score=collaboration_score,
            emergent_properties=emergent_properties
        )
    
    def _update_combination_metrics(self, response: CollaborativeResponse, processing_time: float):
        """Update combination performance metrics"""
        self.combination_metrics['responses_combined'] += 1
        self.combination_metrics['emergent_properties_detected'] += len(response.emergent_properties)
        
        current_avg = self.combination_metrics['average_combination_time']
        total_responses = self.combination_metrics['responses_combined']
        
        new_avg = ((current_avg * (total_responses - 1)) + processing_time) / total_responses
        self.combination_metrics['average_combination_time'] = new_avg
        
        current_effectiveness = self.combination_metrics['collaboration_effectiveness']
        new_effectiveness = ((current_effectiveness * (total_responses - 1)) + response.collaboration_score) / total_responses
        self.combination_metrics['collaboration_effectiveness'] = new_effectiveness
    
    def get_combination_metrics(self) -> Dict[str, Any]:
        """Get combination performance metrics"""
        return {
            'responses_combined': self.combination_metrics['responses_combined'],
            'average_combination_time': self.combination_metrics['average_combination_time'],
            'collaboration_effectiveness': self.combination_metrics['collaboration_effectiveness'],
            'emergent_properties_detected': self.combination_metrics['emergent_properties_detected'],
            'emergence_rate': self.combination_metrics['emergent_properties_detected'] / max(1, self.combination_metrics['responses_combined'])
        }


class RoutingPerformanceMonitor:
    """Performance monitoring and optimization for MoE routing"""
    
    def __init__(self):
        self.performance_metrics = {
            'total_queries_processed': 0,
            'average_response_time': 0.0,
            'success_rate': 1.0,
            'expert_utilization': defaultdict(int),
            'domain_distribution': defaultdict(int),
            'collaboration_requests': 0,
            'emergent_properties_count': 0
        }
        
        self.performance_history = []
        self.optimization_cycles = 0
    
    def record_query_performance(self, query: Query, response: ExpertResponse, processing_time: float):
        """Record performance metrics for a single query"""
        self.performance_metrics['total_queries_processed'] += 1
        
        current_avg = self.performance_metrics['average_response_time']
        total_queries = self.performance_metrics['total_queries_processed']
        
        new_avg = ((current_avg * (total_queries - 1)) + processing_time) / total_queries
        self.performance_metrics['average_response_time'] = new_avg
        
        current_success_rate = self.performance_metrics['success_rate']
        new_success = 1.0 if response.success else 0.0
        self.performance_metrics['success_rate'] = (
            (current_success_rate * (total_queries - 1) + new_success) / total_queries
        )
        
        self.performance_metrics['expert_utilization'][response.expert_name] += 1
        
        domain = query.domain or 'general'
        self.performance_metrics['domain_distribution'][domain] += 1
        
        self.performance_history.append({
            'timestamp': time.time(),
            'processing_time': processing_time,
            'success': response.success,
            'expert': response.expert_name,
            'domain': domain,
            'confidence': response.confidence_score
        })
        
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-500:]
    
    def record_collaboration_performance(self, query: Query, response: CollaborativeResponse, processing_time: float):
        """Record performance metrics for collaborative responses"""
        self.performance_metrics['collaboration_requests'] += 1
        self.performance_metrics['emergent_properties_count'] += len(response.emergent_properties)
        
        mock_expert_response = ExpertResponse(
            expert_name=response.primary_expert,
            response_content=response.combined_response,
            confidence_score=response.collaboration_score,
            mathematical_reasoning='Collaborative reasoning',
            processing_time=processing_time,
            success=True
        )
        
        self.record_query_performance(query, mock_expert_response, processing_time)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        total_queries = self.performance_metrics['total_queries_processed']
        collaboration_rate = (
            self.performance_metrics['collaboration_requests'] / max(1, total_queries)
        )
        emergence_rate = (
            self.performance_metrics['emergent_properties_count'] / 
            max(1, self.performance_metrics['collaboration_requests'])
        )
        
        expert_utilization = dict(self.performance_metrics['expert_utilization'])
        if expert_utilization:
            max_utilization = max(expert_utilization.values())
            expert_balance = 1.0 - (max_utilization - min(expert_utilization.values())) / max_utilization
        else:
            expert_balance = 1.0
        
        return {
            'total_queries_processed': total_queries,
            'average_response_time': self.performance_metrics['average_response_time'],
            'success_rate': self.performance_metrics['success_rate'],
            'collaboration_rate': collaboration_rate,
            'emergence_rate': emergence_rate,
            'expert_balance': expert_balance,
            'expert_utilization': expert_utilization,
            'domain_distribution': dict(self.performance_metrics['domain_distribution']),
            'optimization_cycles': self.optimization_cycles
        }
    
    def optimize_performance(self, feedback: Dict[str, float]) -> Dict[str, Any]:
        """Optimize routing performance based on feedback"""
        optimization_results = {}
        self.optimization_cycles += 1
        
        if len(self.performance_history) >= 10:
            recent_performance = self.performance_history[-10:]
            avg_recent_time = np.mean([p['processing_time'] for p in recent_performance])
            recent_success_rate = np.mean([p['success'] for p in recent_performance])
            
            if avg_recent_time > self.performance_metrics['average_response_time'] * 1.2:
                optimization_results['response_time_optimization'] = 'applied'
                
            if recent_success_rate < 0.9:
                optimization_results['reliability_optimization'] = 'applied'
        
        return optimization_results


class MoERouter:
    """Main MoE routing logic with domain-aware expert selection"""
    
    def __init__(self, persona_experts: Dict[str, Any]):
        self.persona_experts = persona_experts
        
        self.classifier = DomainClassifier()
        self.selector = ExpertSelector(persona_experts)
        self.combiner = ResponseCombiner()
        self.monitor = RoutingPerformanceMonitor()
        
        self.qr3_cache = None
        self.qr3_memory_optimizer = None
        self.qr3_enabled = False
        
        self.routing_config = {
            'enable_collaboration': True,
            'collaboration_threshold': 0.3,
            'max_experts_per_query': 3,
            'enable_caching': False,
            'cache_ttl': 300
        }
        
        logger.info(f"MoERouter initialized with {len(persona_experts)} experts")
    
    def enable_qr3_optimizations(self, cache_manager=None, memory_optimizer=None):
        """Enable QR3Full Phase 1 optimizations"""
        try:
            if cache_manager:
                self.qr3_cache = cache_manager
                self.routing_config['enable_caching'] = True
                logger.info("QR3Full caching enabled")
            
            if memory_optimizer:
                self.qr3_memory_optimizer = memory_optimizer
                logger.info("QR3Full memory optimization enabled")
            
            self.qr3_enabled = True
            return True
            
        except Exception as e:
            logger.error(f"Failed to enable QR3 optimizations: {e}")
            return False
    
    def route_query(self, query: Query) -> ExpertResponse:
        """Route query to most appropriate persona expert"""
        start_time = time.time()
        
        try:
            if self.qr3_enabled and self.qr3_cache:
                cache_key = f"query_{hash(query.content)}_{query.domain}"
                cached_response = self.qr3_cache.get_cached_result(cache_key)
                
                if cached_response:
                    logger.debug("Cache hit for query routing")
                    return cached_response
            
            domain = self.classifier.classify_domain(query)
            expert_name = self.selector.select_expert(domain, query)
            expert = self.persona_experts[expert_name]
            
            if self.qr3_enabled and self.qr3_memory_optimizer:
                @self.qr3_memory_optimizer.memory_efficient_decorator
                def process_with_optimization():
                    return expert.process_query(query)
                
                response = process_with_optimization()
            else:
                response = expert.process_query(query)
            
            self.selector.update_expert_performance(expert_name, response)
            
            if self.qr3_enabled and self.qr3_cache and response.success:
                cache_key = f"query_{hash(query.content)}_{query.domain}"
                self.qr3_cache.cache_result(cache_key, response)
            
            processing_time = time.time() - start_time
            self.monitor.record_query_performance(query, response, processing_time)
            
            return response
            
        except Exception as e:
            error_response = ExpertResponse(
                expert_name='error_handler',
                response_content={'error': str(e)},
                confidence_score=0.0,
                mathematical_reasoning='Error occurred during processing',
                processing_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )
            
            self.monitor.record_query_performance(query, error_response, time.time() - start_time)
            return error_response
    
    def multi_expert_collaboration(self, query: Query, max_experts: int = None) -> CollaborativeResponse:
        """Enable multiple experts to collaborate on complex problems"""
        start_time = time.time()
        max_experts = max_experts or self.routing_config['max_experts_per_query']
        
        try:
            if self.qr3_enabled and self.qr3_cache:
                cache_key = f"collab_{hash(query.content)}_{max_experts}"
                cached_response = self.qr3_cache.get_cached_result(cache_key)
                
                if cached_response:
                    logger.debug("Cache hit for collaborative query")
                    return cached_response
            
            selected_experts = self.selector.select_multiple_experts(query, max_experts)
            
            responses = []
            for expert_name in selected_experts:
                expert = self.persona_experts[expert_name]
                
                try:
                    if self.qr3_enabled and self.qr3_memory_optimizer:
                        @self.qr3_memory_optimizer.memory_efficient_decorator
                        def process_expert():
                            return expert.process_query(query)
                        
                        response = process_expert()
                    else:
                        response = expert.process_query(query)
                    
                    responses.append(response)
                    self.selector.update_expert_performance(expert_name, response)
                    
                except Exception as e:
                    logger.warning(f"Expert {expert_name} failed: {e}")
                    continue
            
            collaborative_response = self.combiner.combine_responses(responses)
            
            if self.qr3_enabled and self.qr3_cache and collaborative_response.collaboration_score > 0.5:
                cache_key = f"collab_{hash(query.content)}_{max_experts}"
                self.qr3_cache.cache_result(cache_key, collaborative_response)
            
            processing_time = time.time() - start_time
            self.monitor.record_collaboration_performance(query, collaborative_response, processing_time)
            
            return collaborative_response
            
        except Exception as e:
            logger.warning(f"Collaboration failed, falling back to single expert: {e}")
            single_response = self.route_query(query)
            
            return CollaborativeResponse(
                primary_expert=single_response.expert_name,
                contributing_experts=[single_response.expert_name],
                combined_response=single_response.response_content,
                collaboration_score=single_response.confidence_score,
                emergent_properties=['fallback_mode'],
                processing_time=time.time() - start_time
            )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive routing performance metrics"""
        base_metrics = self.monitor.get_performance_metrics()
        
        base_metrics.update({
            'classification_metrics': self.classifier.get_classification_metrics(),
            'expert_load_status': self.selector.get_expert_load_status(),
            'combination_metrics': self.combiner.get_combination_metrics(),
            'routing_config': self.routing_config,
            'qr3_enabled': self.qr3_enabled
        })
        
        return base_metrics
    
    def optimize_routing_performance(self, feedback: Dict[str, float]) -> Dict[str, Any]:
        """Optimize routing performance based on feedback"""
        optimization_results = {}
        
        monitor_results = self.monitor.optimize_performance(feedback)
        optimization_results['monitor'] = monitor_results
        
        if 'accuracy_improvement' in feedback:
            self.selector.optimize_selection_weights(feedback)
            optimization_results['selector'] = 'weights_optimized'
        
        if feedback.get('response_time_improvement', 0) < 0.1:
            self.routing_config['enable_caching'] = True
            optimization_results['caching'] = 'enabled'
        
        if feedback.get('collaboration_effectiveness', 0) > 0.8:
            self.routing_config['max_experts_per_query'] = min(5, self.routing_config['max_experts_per_query'] + 1)
            optimization_results['collaboration'] = 'expanded'
        
        return optimization_results
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get optimization recommendations"""
        recommendations = []
        metrics = self.get_performance_metrics()
        
        if metrics['classification_metrics']['average_classification_time'] > 0.1:
            recommendations.append("Optimize domain classification patterns for faster routing")
        
        if metrics['combination_metrics']['collaboration_effectiveness'] < 0.7:
            recommendations.append("Improve response combination strategies")
        
        if not self.qr3_enabled:
            recommendations.append("Enable QR3Full Phase 1 optimizations for 25-35% performance improvement")
        
        return recommendations


if __name__ == '__main__':
    print("🧠 MoE Routing System - Fixed Version")
    print("✅ All syntax errors resolved")
    print("🚀 Ready for integration with QR3Full optimizations")