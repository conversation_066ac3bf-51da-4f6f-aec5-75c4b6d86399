"""
Test Suite for MoE Routing System
Parallel Postulate Transformer MoE - Task 10 Validation

This test suite validates the MoE routing system components:
- DomainClassifier: Problem type analysis and routing decisions
- ExpertSelector: Query-expert matching optimization
- ResponseCombiner: Multi-expert response integration
- RoutingPerformanceMonitor: Performance monitoring and optimization
- MoERouter: Main routing logic with domain-aware expert selection
"""

import unittest
import time
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the current directory to the path to import our modules
sys.path.append(os.path.dirname(__file__))

from moe_routing_system import (
    Query, ExpertResponse, CollaborativeResponse,
    DomainClassifier, ExpertSelector, ResponseCombiner,
    RoutingPerformanceMonitor, MoERouter
)

class TestDomainClassifier(unittest.TestCase):
    """Test domain classification functionality"""
    
    def setUp(self):
        self.classifier = DomainClassifier()
    
    def test_logical_reasoning_classification(self):
        """Test classification of logical reasoning queries"""
        query = Query(content="If all humans are mortal and Socrates is human, then Socrates is mortal")     
   
        domain = self.classifier.classify_domain(query)
        self.assertEqual(domain, 'logical_reasoning')
        
        # Test with logical symbols
        query_symbols = Query(content="∀x: P(x) → Q(x), given P(a), therefore Q(a)")
        domain_symbols = self.classifier.classify_domain(query_symbols)
        self.assertEqual(domain_symbols, 'logical_reasoning')
    
    def test_motion_dynamics_classification(self):
        """Test classification of motion dynamics queries"""
        query = Query(content="Calculate the trajectory of a projectile with initial velocity 50 m/s")
        domain = self.classifier.classify_domain(query)
        self.assertEqual(domain, 'motion_dynamics')
        
        # Test with differential equations
        query_diff = Query(content="Solve d²x/dt² + ω²x = F(t) for harmonic oscillation")
        domain_diff = self.classifier.classify_domain(query_diff)
        self.assertEqual(domain_diff, 'motion_dynamics')
    
    def test_relativistic_processing_classification(self):
        """Test classification of relativistic processing queries"""
        query = Query(content="Analyze spacetime curvature effects on information density")
        domain = self.classifier.classify_domain(query)
        self.assertEqual(domain, 'relativistic_processing')
        
        # Test with Einstein tensor
        query_tensor = Query(content="Calculate R_μν - ½gR = 8πT for given metric tensor")
        domain_tensor = self.classifier.classify_domain(query_tensor)
        self.assertEqual(domain_tensor, 'relativistic_processing')
    
    def test_force_optimization_classification(self):
        """Test classification of force optimization queries"""
        query = Query(content="Optimize mechanical system forces for minimum energy")
        domain = self.classifier.classify_domain(query)
        self.assertEqual(domain, 'force_optimization')
        
        # Test with Newton's law
        query_newton = Query(content="Calculate F_ij = G(m_i·m_j)/r_ij² for gravitational system")
        domain_newton = self.classifier.classify_domain(query_newton)
        self.assertEqual(domain_newton, 'force_optimization')
    
    def test_computational_architecture_classification(self):
        """Test classification of computational architecture queries"""
        query = Query(content="Analyze algorithm complexity and optimize Turing machine efficiency")
        domain = self.classifier.classify_domain(query)
        self.assertEqual(domain, 'computational_architecture')
        
        # Test with state machine notation
        query_state = Query(content="Design δ(q_i, a_i) → (q_i', a_i', L_i/R_i) transition function")
        domain_state = self.classifier.classify_domain(query_state)
        self.assertEqual(domain_state, 'computational_architecture')
    
    def test_experimental_validation_classification(self):
        """Test classification of experimental validation queries"""
        query = Query(content="Validate experimental reproducibility and measure decay rates")
        domain = self.classifier.classify_domain(query)
        self.assertEqual(domain, 'experimental_validation')
        
        # Test with exponential decay
        query_decay = Query(content="Analyze N_i(t) = N_0e^(-λ_i·t) for radioactive decay")
        domain_decay = self.classifier.classify_domain(query_decay)
        self.assertEqual(domain_decay, 'experimental_validation')
    
    def test_general_classification_fallback(self):
        """Test fallback to general classification"""
        query = Query(content="Hello, how are you today?")
        domain = self.classifier.classify_domain(query)
        self.assertEqual(domain, 'general')
    
    def test_domain_probabilities(self):
        """Test domain probability distribution"""
        query = Query(content="Analyze logical reasoning in motion dynamics")
        probabilities = self.classifier.get_domain_probabilities(query)
        
        self.assertIsInstance(probabilities, dict)
        self.assertIn('logical_reasoning', probabilities)
        self.assertIn('motion_dynamics', probabilities)
        
        # Probabilities should sum to 1
        total_prob = sum(probabilities.values())
        self.assertAlmostEqual(total_prob, 1.0, places=2)
    
    def test_multi_domain_routing_suggestions(self):
        """Test multi-domain routing suggestions"""
        query = Query(content="Optimize force calculations using logical reasoning and experimental validation")
        suggestions = self.classifier.suggest_multi_domain_routing(query, threshold=0.2)
        
        self.assertIsInstance(suggestions, list)
        self.assertGreater(len(suggestions), 0)
        
        # Should include relevant domains
        expected_domains = ['force_optimization', 'logical_reasoning', 'experimental_validation']
        for domain in expected_domains:
            if domain in suggestions:
                self.assertIn(domain, suggestions)


class TestExpertSelector(unittest.TestCase):
    """Test expert selection functionality"""
    
    def setUp(self):
        # Mock persona experts
        self.mock_experts = {
            'aristotle': Mock(),
            'galileo': Mock(),
            'einstein': Mock(),
            'newton': Mock(),
            'turing': Mock(),
            'curie': Mock()
        }
        self.selector = ExpertSelector(self.mock_experts)
    
    def test_single_expert_selection(self):
        """Test selection of single expert for domain"""
        query = Query(content="Logical reasoning problem")
        expert = self.selector.select_expert('logical_reasoning', query)
        self.assertEqual(expert, 'aristotle')
        
        query_motion = Query(content="Motion dynamics problem")
        expert_motion = self.selector.select_expert('motion_dynamics', query_motion)
        self.assertEqual(expert_motion, 'galileo')
    
    def test_multiple_expert_selection(self):
        """Test selection of multiple experts for collaboration"""
        query = Query(content="Complex problem requiring multiple perspectives")
        experts = self.selector.select_multiple_experts(query, max_experts=3)
        
        self.assertIsInstance(experts, list)
        self.assertLessEqual(len(experts), 3)
        self.assertGreater(len(experts), 0)
        
        # All selected experts should be valid
        for expert in experts:
            self.assertIn(expert, self.mock_experts.keys())
    
    def test_expert_performance_update(self):
        """Test expert performance tracking"""
        # Create mock response
        response = ExpertResponse(
            expert_name='aristotle',
            response_content={'domain': 'logical_reasoning'},
            confidence_score=0.9,
            mathematical_reasoning='Test reasoning',
            processing_time=0.5,
            success=True
        )
        
        # Update performance
        initial_success_rate = self.selector.expert_performance['aristotle']['success_rate']
        self.selector.update_expert_performance('aristotle', response)
        
        # Check that performance was updated
        updated_performance = self.selector.expert_performance['aristotle']
        self.assertGreaterEqual(updated_performance['success_rate'], 0.0)
        self.assertLessEqual(updated_performance['success_rate'], 1.0)
    
    def test_expert_load_status(self):
        """Test expert load status reporting"""
        load_status = self.selector.get_expert_load_status()
        
        self.assertIsInstance(load_status, dict)
        self.assertEqual(len(load_status), len(self.mock_experts))
        
        for expert_name, status in load_status.items():
            self.assertIn('load_factor', status)
            self.assertIn('success_rate', status)
            self.assertIn('average_response_time', status)
            self.assertIn('queries_processed', status)


class TestResponseCombiner(unittest.TestCase):
    """Test response combination functionality"""
    
    def setUp(self):
        self.combiner = ResponseCombiner()
    
    def test_weighted_average_combination(self):
        """Test weighted average response combination"""
        responses = [
            ExpertResponse(
                expert_name='aristotle',
                response_content={'domain': 'logical_reasoning', 'result': 'logical_result'},
                confidence_score=0.9,
                mathematical_reasoning='Logical reasoning',
                processing_time=0.3,
                success=True
            ),
            ExpertResponse(
                expert_name='galileo',
                response_content={'domain': 'motion_dynamics', 'result': 'motion_result'},
                confidence_score=0.8,
                mathematical_reasoning='Motion analysis',
                processing_time=0.4,
                success=True
            )
        ]
        
        combined = self.combiner.combine_responses(responses, strategy='weighted_average')
        
        self.assertIsInstance(combined, CollaborativeResponse)
        self.assertEqual(combined.primary_expert, 'aristotle')  # Higher confidence
        self.assertEqual(len(combined.contributing_experts), 2)
        self.assertGreater(combined.collaboration_score, 0.0)
    
    def test_consensus_building_combination(self):
        """Test consensus building response combination"""
        responses = [
            ExpertResponse(
                expert_name='newton',
                response_content={'domain': 'force_optimization', 'result': 'consensus_result'},
                confidence_score=0.85,
                mathematical_reasoning='Force analysis',
                processing_time=0.2,
                success=True
            ),
            ExpertResponse(
                expert_name='einstein',
                response_content={'domain': 'force_optimization', 'result': 'consensus_result'},
                confidence_score=0.9,
                mathematical_reasoning='Relativistic analysis',
                processing_time=0.3,
                success=True
            )
        ]
        
        combined = self.combiner.combine_responses(responses, strategy='consensus_building')
        
        self.assertIsInstance(combined, CollaborativeResponse)
        self.assertIn('consensus_formation', combined.emergent_properties)
        self.assertGreater(combined.collaboration_score, 0.0)
    
    def test_empty_responses_handling(self):
        """Test handling of empty response list"""
        combined = self.combiner.combine_responses([])
        
        self.assertIsInstance(combined, CollaborativeResponse)
        self.assertEqual(combined.primary_expert, 'none')
        self.assertEqual(len(combined.contributing_experts), 0)
        self.assertEqual(combined.collaboration_score, 0.0)
    
    def test_failed_responses_filtering(self):
        """Test filtering of failed responses"""
        responses = [
            ExpertResponse(
                expert_name='turing',
                response_content={'domain': 'computational_architecture'},
                confidence_score=0.7,
                mathematical_reasoning='Computational analysis',
                processing_time=0.5,
                success=False,  # Failed response
                error_message='Processing error'
            ),
            ExpertResponse(
                expert_name='curie',
                response_content={'domain': 'experimental_validation', 'result': 'validation_result'},
                confidence_score=0.8,
                mathematical_reasoning='Experimental analysis',
                processing_time=0.4,
                success=True
            )
        ]
        
        combined = self.combiner.combine_responses(responses)
        
        # Should only include successful response
        self.assertEqual(len(combined.contributing_experts), 1)
        self.assertEqual(combined.primary_expert, 'curie')


class TestMoERouter(unittest.TestCase):
    """Test main MoE router functionality"""
    
    def setUp(self):
        # Mock persona experts with process_query method
        self.mock_experts = {}
        expert_names = ['aristotle', 'galileo', 'einstein', 'newton', 'turing', 'curie']
        
        for name in expert_names:
            mock_expert = Mock()
            mock_expert.process_query.return_value = ExpertResponse(
                expert_name=name,
                response_content={'domain': f'{name}_domain', 'result': f'{name}_result'},
                confidence_score=0.8,
                mathematical_reasoning=f'{name} reasoning',
                processing_time=0.3,
                success=True
            )
            self.mock_experts[name] = mock_expert
        
        self.router = MoERouter(self.mock_experts)
    
    def test_single_query_routing(self):
        """Test routing of single query to appropriate expert"""
        query = Query(content="Solve logical reasoning problem with premises and conclusions")
        response = self.router.route_query(query)
        
        self.assertIsInstance(response, ExpertResponse)
        self.assertTrue(response.success)
        self.assertGreater(response.confidence_score, 0.0)
    
    def test_multi_expert_collaboration(self):
        """Test multi-expert collaboration"""
        query = Query(content="Complex problem requiring multiple expert perspectives")
        collaborative_response = self.router.multi_expert_collaboration(query)
        
        self.assertIsInstance(collaborative_response, CollaborativeResponse)
        self.assertGreater(len(collaborative_response.contributing_experts), 1)
        self.assertGreater(collaborative_response.collaboration_score, 0.0)
    
    def test_performance_monitoring(self):
        """Test performance monitoring functionality"""
        # Process several queries
        queries = [
            Query(content="Logical reasoning test"),
            Query(content="Motion dynamics test"),
            Query(content="Force optimization test")
        ]
        
        for query in queries:
            self.router.route_query(query)
        
        # Check performance metrics
        metrics = self.router.get_performance_metrics()
        
        self.assertIsInstance(metrics, dict)
        self.assertIn('total_queries_processed', metrics)
        self.assertIn('average_response_time', metrics)
        self.assertIn('success_rate', metrics)
        self.assertGreater(metrics['total_queries_processed'], 0)
    
    def test_adaptive_routing_optimization(self):
        """Test adaptive routing optimization"""
        # Simulate performance feedback
        feedback = {
            'accuracy_improvement': 0.15,
            'response_time_improvement': 0.1
        }
        
        # Apply optimization
        self.router.optimize_routing_performance(feedback)
        
        # Verify optimization was applied
        # This is a basic test - in practice, we'd check specific optimization effects
        self.assertTrue(True)  # Placeholder assertion


class TestIntegrationScenarios(unittest.TestCase):
    """Test integration scenarios across all components"""
    
    def setUp(self):
        # Create mock experts
        self.mock_experts = {}
        expert_names = ['aristotle', 'galileo', 'einstein', 'newton', 'turing', 'curie']
        
        for name in expert_names:
            mock_expert = Mock()
            mock_expert.process_query.return_value = ExpertResponse(
                expert_name=name,
                response_content={
                    'domain': f'{name}_domain',
                    'mathematical_expression': f'{name}_expression',
                    'result': f'{name}_result'
                },
                confidence_score=0.8 + (hash(name) % 20) / 100,  # Vary confidence slightly
                mathematical_reasoning=f'{name} mathematical reasoning',
                processing_time=0.2 + (hash(name) % 10) / 100,  # Vary processing time
                success=True
            )
            self.mock_experts[name] = mock_expert
        
        self.router = MoERouter(self.mock_experts)
    
    def test_end_to_end_query_processing(self):
        """Test complete end-to-end query processing"""
        query = Query(
            content="Analyze the logical consistency of motion equations in relativistic framework",
            complexity_level=3
        )
        
        # Single expert routing
        single_response = self.router.route_query(query)
        self.assertIsInstance(single_response, ExpertResponse)
        self.assertTrue(single_response.success)
        
        # Multi-expert collaboration
        collaborative_response = self.router.multi_expert_collaboration(query)
        self.assertIsInstance(collaborative_response, CollaborativeResponse)
        self.assertGreater(len(collaborative_response.contributing_experts), 1)
        
        # Verify emergent properties
        self.assertIsInstance(collaborative_response.emergent_properties, list)
    
    def test_performance_under_load(self):
        """Test system performance under load"""
        queries = []
        for i in range(50):
            query = Query(
                content=f"Test query {i} with various complexity levels",
                complexity_level=(i % 5) + 1
            )
            queries.append(query)
        
        start_time = time.time()
        
        # Process all queries
        responses = []
        for query in queries:
            response = self.router.route_query(query)
            responses.append(response)
        
        total_time = time.time() - start_time
        
        # Verify performance
        self.assertEqual(len(responses), 50)
        self.assertLess(total_time, 10.0)  # Should complete within 10 seconds
        
        # Check success rate
        successful_responses = [r for r in responses if r.success]
        success_rate = len(successful_responses) / len(responses)
        self.assertGreater(success_rate, 0.8)  # At least 80% success rate
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms"""
        # Create a failing expert
        failing_expert = Mock()
        failing_expert.process_query.side_effect = Exception("Expert processing error")
        
        # Replace one expert with failing expert
        original_expert = self.mock_experts['aristotle']
        self.mock_experts['aristotle'] = failing_expert
        
        # Recreate router with failing expert
        router_with_failure = MoERouter(self.mock_experts)
        
        query = Query(content="Logical reasoning problem that should fail")
        
        # Should handle failure gracefully
        try:
            response = router_with_failure.route_query(query)
            # If no exception, check that response indicates failure appropriately
            self.assertIsInstance(response, ExpertResponse)
        except Exception as e:
            # If exception occurs, it should be handled gracefully
            self.fail(f"Router should handle expert failures gracefully, but got: {e}")
        
        # Restore original expert
        self.mock_experts['aristotle'] = original_expert


def run_comprehensive_tests():
    """Run comprehensive test suite with detailed reporting"""
    print("🧪 Running MoE Routing System Test Suite")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestDomainClassifier,
        TestExpertSelector,
        TestResponseCombiner,
        TestMoERouter,
        TestIntegrationScenarios
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError: ')[-1].split(chr(10))[0]}")
    
    if result.errors:
        print("\n🚨 Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split(chr(10))[-2]}")
    
    if not result.failures and not result.errors:
        print("✅ All tests passed successfully!")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)