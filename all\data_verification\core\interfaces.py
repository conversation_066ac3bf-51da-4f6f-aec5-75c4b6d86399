"""
Abstract base classes and interfaces for the Data Verification System.

This module defines the core interfaces that all components must implement,
providing a consistent API and enabling extensibility through the plugin system.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Iterator, List, Optional, Union
from .models import ValidationError, ValidationResult, ValidationConfig


class FileReader(ABC):
    """
    Abstract base class for file readers.
    
    File readers are responsible for parsing different file formats and
    yielding data rows in a consistent format for validation.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the file reader with optional configuration.
        
        Args:
            config: Optional configuration dictionary for the reader
        """
        self.config = config or {}
    
    @abstractmethod
    def read(self, file_path: str) -> Iterator[Dict[str, Any]]:
        """
        Read and yield data rows from the specified file.
        
        Args:
            file_path: Path to the file to read
            
        Yields:
            Dict[str, Any]: Each row as a dictionary with column names as keys
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file format is invalid or corrupted
            IOError: If there are issues reading the file
        """
        pass
    
    @abstractmethod
    def get_headers(self, file_path: str) -> List[str]:
        """
        Get column headers from the file.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            List[str]: List of column header names
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If headers cannot be determined
        """
        pass
    
    @abstractmethod
    def validate_file_format(self, file_path: str) -> bool:
        """
        Validate that the file format is supported by this reader.
        
        Args:
            file_path: Path to the file to validate
            
        Returns:
            bool: True if the file format is supported, False otherwise
        """
        pass
    
    @abstractmethod
    def get_row_count(self, file_path: str) -> int:
        """
        Get the total number of data rows in the file (excluding headers).
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            int: Number of data rows in the file
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file cannot be processed
        """
        pass
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get comprehensive information about the file.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            Dict[str, Any]: File information including headers, row count, size, etc.
        """
        import os
        from datetime import datetime
        
        try:
            stat = os.stat(file_path)
            return {
                'file_path': file_path,
                'file_size_bytes': stat.st_size,
                'file_size_mb': stat.st_size / (1024 * 1024),
                'last_modified': datetime.fromtimestamp(stat.st_mtime),
                'headers': self.get_headers(file_path),
                'row_count': self.get_row_count(file_path),
                'format_valid': self.validate_file_format(file_path)
            }
        except Exception as e:
            return {
                'file_path': file_path,
                'error': str(e),
                'format_valid': False
            }


class Validator(ABC):
    """
    Abstract base class for data validators.
    
    Validators are responsible for checking data values against specific rules
    and returning validation results with detailed error information.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the validator with optional configuration.
        
        Args:
            config: Optional configuration dictionary for the validator
        """
        self.config = config or {}
        self.name = self.__class__.__name__
    
    @abstractmethod
    def validate(self, value: Any, context: Dict[str, Any]) -> ValidationResult:
        """
        Validate a single value against the configured rules.
        
        Args:
            value: The value to validate
            context: Additional context information including:
                - file_path: Path to the file being processed
                - row_number: Current row number
                - column_name: Name of the column being validated
                - row_data: Complete row data as dictionary
                
        Returns:
            ValidationResult: Result of the validation with any errors found
        """
        pass
    
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """
        Return JSON schema for validator configuration.
        
        Returns:
            Dict[str, Any]: JSON schema describing valid configuration options
        """
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """
        Get list of data types this validator can handle.
        
        Returns:
            List[str]: List of supported data type names (e.g., 'int', 'float', 'str')
        """
        pass
    
    def get_validator_info(self) -> Dict[str, Any]:
        """
        Get information about this validator.
        
        Returns:
            Dict[str, Any]: Validator metadata including name, supported types, etc.
        """
        return {
            'name': self.name,
            'supported_types': self.get_supported_types(),
            'config_schema': self.get_config_schema(),
            'description': self.__doc__ or "No description available"
        }
    
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate the provided configuration against the schema.
        
        Args:
            config: Configuration dictionary to validate
            
        Returns:
            List[str]: List of validation error messages (empty if valid)
        """
        # Basic implementation - can be overridden for more sophisticated validation
        schema = self.get_config_schema()
        errors = []
        
        # Check required fields
        required_fields = schema.get('required', [])
        for field in required_fields:
            if field not in config:
                errors.append(f"Required field '{field}' is missing")
        
        # Check field types (basic validation)
        properties = schema.get('properties', {})
        for field, value in config.items():
            if field in properties:
                expected_type = properties[field].get('type')
                if expected_type and not self._check_type(value, expected_type):
                    errors.append(f"Field '{field}' has invalid type. Expected {expected_type}")
        
        return errors
    
    def _check_type(self, value: Any, expected_type: str) -> bool:
        """Helper method to check if value matches expected JSON schema type."""
        type_mapping = {
            'string': str,
            'number': (int, float),
            'integer': int,
            'boolean': bool,
            'array': list,
            'object': dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        return True  # Unknown type, assume valid


class ErrorReporter(ABC):
    """
    Abstract base class for error reporters.
    
    Error reporters are responsible for outputting validation results and errors
    in various formats (console, file, API, etc.).
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the error reporter with optional configuration.
        
        Args:
            config: Optional configuration dictionary for the reporter
        """
        self.config = config or {}
    
    @abstractmethod
    def report_error(self, error: ValidationError) -> None:
        """
        Report a single validation error.
        
        Args:
            error: The validation error to report
        """
        pass
    
    @abstractmethod
    def report_result(self, result: ValidationResult) -> None:
        """
        Report the complete validation result for a file.
        
        Args:
            result: The validation result to report
        """
        pass
    
    @abstractmethod
    def generate_summary(self, errors: List[ValidationError]) -> str:
        """
        Generate a summary report from a list of validation errors.
        
        Args:
            errors: List of validation errors to summarize
            
        Returns:
            str: Formatted summary report
        """
        pass
    
    @abstractmethod
    def initialize(self) -> None:
        """
        Initialize the reporter (e.g., create output directories, open files).
        
        This method is called before any reporting begins.
        """
        pass
    
    @abstractmethod
    def finalize(self) -> None:
        """
        Finalize the reporter (e.g., close files, send final reports).
        
        This method is called after all reporting is complete.
        """
        pass
    
    def report_batch_result(self, batch_result) -> None:
        """
        Report the results of a batch validation operation.
        
        Args:
            batch_result: BatchValidationResult containing all file results
        """
        # Default implementation reports each file result individually
        for result in batch_result.results:
            self.report_result(result)
    
    def get_reporter_info(self) -> Dict[str, Any]:
        """
        Get information about this reporter.
        
        Returns:
            Dict[str, Any]: Reporter metadata including name, output format, etc.
        """
        return {
            'name': self.__class__.__name__,
            'description': self.__doc__ or "No description available",
            'config': self.config
        }


class ValidatorFactory(ABC):
    """
    Abstract factory for creating validator instances.
    
    This interface allows for dynamic validator creation based on configuration.
    """
    
    @abstractmethod
    def create_validator(self, validator_type: str, config: Dict[str, Any]) -> Validator:
        """
        Create a validator instance of the specified type.
        
        Args:
            validator_type: Type of validator to create
            config: Configuration for the validator
            
        Returns:
            Validator: Configured validator instance
            
        Raises:
            ValueError: If the validator type is not supported
        """
        pass
    
    @abstractmethod
    def get_supported_validators(self) -> List[str]:
        """
        Get list of supported validator types.
        
        Returns:
            List[str]: List of validator type names
        """
        pass


class FileReaderFactory(ABC):
    """
    Abstract factory for creating file reader instances.
    
    This interface allows for dynamic file reader creation based on file type.
    """
    
    @abstractmethod
    def create_reader(self, file_path: str, config: Dict[str, Any]) -> FileReader:
        """
        Create a file reader instance appropriate for the file type.
        
        Args:
            file_path: Path to the file to read
            config: Configuration for the reader
            
        Returns:
            FileReader: Configured file reader instance
            
        Raises:
            ValueError: If no suitable reader is found for the file type
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported file formats.
        
        Returns:
            List[str]: List of file format extensions (e.g., ['.csv', '.json'])
        """
        pass


class ErrorReporterFactory(ABC):
    """
    Abstract factory for creating error reporter instances.
    
    This interface allows for dynamic error reporter creation based on output format.
    """
    
    @abstractmethod
    def create_reporter(self, reporter_type: str, config: Dict[str, Any]) -> ErrorReporter:
        """
        Create an error reporter instance of the specified type.
        
        Args:
            reporter_type: Type of reporter to create
            config: Configuration for the reporter
            
        Returns:
            ErrorReporter: Configured error reporter instance
            
        Raises:
            ValueError: If the reporter type is not supported
        """
        pass
    
    @abstractmethod
    def get_supported_reporters(self) -> List[str]:
        """
        Get list of supported reporter types.
        
        Returns:
            List[str]: List of reporter type names
        """
        pass


class ValidationPlugin(ABC):
    """
    Abstract base class for validation system plugins.
    
    Plugins can extend the system with new validators, readers, or reporters.
    """
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the plugin name."""
        pass
    
    @abstractmethod
    def get_version(self) -> str:
        """Get the plugin version."""
        pass
    
    @abstractmethod
    def initialize(self, system_config: ValidationConfig) -> None:
        """
        Initialize the plugin with system configuration.
        
        Args:
            system_config: The main system configuration
        """
        pass
    
    @abstractmethod
    def register_components(self, registry: Dict[str, Any]) -> None:
        """
        Register plugin components with the system registry.
        
        Args:
            registry: System component registry to register with
        """
        pass
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """
        Get information about this plugin.
        
        Returns:
            Dict[str, Any]: Plugin metadata
        """
        return {
            'name': self.get_name(),
            'version': self.get_version(),
            'description': self.__doc__ or "No description available"
        }