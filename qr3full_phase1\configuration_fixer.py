import json
import os
from pathlib import Path
from typing import Dict, Any, List

class ConfigurationFixer:
    """Fix common configuration issues for Phase 1"""
    
    def __init__(self):
        self.fixes_applied = []
        self.config_backups = {}
    
    def fix_task_configurations(self) -> Dict[str, Any]:
        """Fix configurations for tasks 2, 3, 6 that show 0% accuracy"""
        fixes = {}
        
        # Task 2: Code Quality Analysis
        task2_config = {
            "enabled": True,
            "analyzers": {
                "complexity": {"enabled": True, "threshold": 10},
                "duplication": {"enabled": True, "threshold": 0.1},
                "maintainability": {"enabled": True, "threshold": 0.7}
            },
            "output_format": "detailed",
            "include_suggestions": True
        }
        
        # Task 3: Security Analysis  
        task3_config = {
            "enabled": True,
            "security_rules": {
                "sql_injection": True,
                "xss_detection": True,
                "hardcoded_secrets": True,
                "unsafe_functions": True
            },
            "severity_levels": ["high", "medium", "low"],
            "output_format": "structured"
        }
        
        # Task 6: Documentation Analysis
        task6_config = {
            "enabled": True,
            "documentation_checks": {
                "docstring_coverage": True,
                "comment_quality": True,
                "api_documentation": True
            },
            "coverage_threshold": 0.8,
            "quality_threshold": 0.7
        }
        
        configs = {
            "task2_config.json": task2_config,
            "task3_config.json": task3_config, 
            "task6_config.json": task6_config
        }
        
        config_dir = Path("config/tasks")
        config_dir.mkdir(parents=True, exist_ok=True)
        
        for filename, config in configs.items():
            config_path = config_dir / filename
            
            # Backup existing config
            if config_path.exists():
                with open(config_path, 'r') as f:
                    self.config_backups[filename] = json.load(f)
            
            # Write new config
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            fixes[filename] = "created/updated"
            self.fixes_applied.append(f"Fixed {filename}")
        
        return fixes
    
    def fix_analyzer_settings(self) -> Dict[str, Any]:
        """Fix analyzer settings that cause performance issues"""
        
        # Optimized analyzer settings
        analyzer_config = {
            "performance_mode": True,
            "parallel_processing": True,
            "max_workers": min(8, os.cpu_count()),
            "memory_limit_mb": 512,
            "timeout_seconds": 30,
            "cache_enabled": True,
            "incremental_analysis": True,
            "skip_large_files": True,
            "large_file_threshold_mb": 10
        }
        
        config_path = Path("config/analyzer_settings.json")
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Backup existing
        if config_path.exists():
            with open(config_path, 'r') as f:
                self.config_backups["analyzer_settings.json"] = json.load(f)
        
        # Write optimized config
        with open(config_path, 'w') as f:
            json.dump(analyzer_config, f, indent=2)
        
        self.fixes_applied.append("Fixed analyzer settings")
        
        return {
            "analyzer_settings": "optimized",
            "performance_mode": True,
            "parallel_processing": True
        }
    
    def fix_logging_configuration(self) -> Dict[str, Any]:
        """Fix logging to reduce overhead"""
        
        logging_config = {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "simple": {
                    "format": "%(levelname)s - %(message)s"
                },
                "detailed": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": "WARNING",
                    "formatter": "simple"
                },
                "file": {
                    "class": "logging.FileHandler",
                    "filename": "logs/analysis.log",
                    "level": "INFO",
                    "formatter": "detailed"
                }
            },
            "loggers": {
                "": {
                    "level": "INFO",
                    "handlers": ["console", "file"]
                }
            }
        }
        
        config_path = Path("config/logging.json")
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create logs directory
        Path("logs").mkdir(exist_ok=True)
        
        with open(config_path, 'w') as f:
            json.dump(logging_config, f, indent=2)
        
        self.fixes_applied.append("Fixed logging configuration")
        
        return {
            "logging_config": "optimized",
            "console_level": "WARNING",
            "file_level": "INFO"
        }
    
    def get_fixes_summary(self) -> Dict[str, Any]:
        """Get summary of all fixes applied"""
        return {
            "fixes_applied": self.fixes_applied,
            "configs_backed_up": list(self.config_backups.keys()),
            "total_fixes": len(self.fixes_applied)
        }

def apply_all_config_fixes():
    """Apply all configuration fixes"""
    fixer = ConfigurationFixer()
    
    results = {}
    results.update(fixer.fix_task_configurations())
    results.update(fixer.fix_analyzer_settings())
    results.update(fixer.fix_logging_configuration())
    
    return {
        "config_fixes": results,
        "summary": fixer.get_fixes_summary()
    }
