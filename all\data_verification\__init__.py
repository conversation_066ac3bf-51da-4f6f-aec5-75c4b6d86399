"""
QR3D Digital World - Data Verification System

A comprehensive Python-based tool designed to validate and verify data integrity 
across various file formats, with initial focus on CSV files. The system provides 
configurable validation rules, detailed error reporting, and extensible architecture 
to support multiple data formats and validation criteria.

This system integrates with the QR3D Digital World project to ensure data quality 
for machine learning pipelines and vector storage operations.
"""

__version__ = "1.0.0"
__author__ = "QR3D Digital World Team"

from .core.models import ValidationError, ValidationResult, ValidationConfig
from .core.interfaces import <PERSON><PERSON>eader, Validator, ErrorReporter
from .core.engine import ValidationEngine

__all__ = [
    'ValidationError',
    'ValidationResult', 
    'ValidationConfig',
    'FileReader',
    'Validator',
    '<PERSON>rrorReporter',
    'ValidationEngine'
]